<template>
  <div id="user-view" class="ma-2">
    <v-row class="d-flex ma-n2">
      <v-col cols="12" md="12" sm="12" class="py-0">
        <v-tabs v-model="userTab" class="user-tabs" left @change="onChangeTab">
          <v-tabs-slider></v-tabs-slider>
          <v-tab v-for="tab in tabs" :key="tab.icon" class="px-10">
            <span class="text-xxl">
              {{ tab.title }}
            </span>
          </v-tab>
          <Refresh
            :page="page"
            :currentTab="userTab"
            :refresh-date="refreshDate"
            @refresh="refresh"
            @showDrawer="onShowDrawer"
          />
        </v-tabs>
      </v-col>
    </v-row>

    <v-tabs-items id="user-tabs-content" v-model="userTab" class="mt-4 pa-1">
      <!-- 危险资产分析 -->
      <v-tab-item>
        <v-row dense>
          <v-col cols="12" md="3" sm="6" style="height: 17rem">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <v-card class="pa-3">
                <v-card-text class="h-full text--primary">
                  <div class="d-flex justify-space-between mb-5 mt-n3">
                    <div class="d-flex justify-center align-center">
                      <vsoc-icon
                        class="mr-2"
                        type="fill"
                        size="1.5rem"
                        icon="icon-shujuzhongxin-Default-W"
                      ></vsoc-icon>
                      <span class="text-xxl text-no-wrap">{{
                        $t('analytics.totalAssets')
                      }}</span>
                    </div>
                    <div class="text-n4xl font-weight-semibold text-no-wrap">
                      {{
                        numberToFormat(
                          dangerAssetData.findVehicleAssetSum,
                          'Object',
                        ).num
                      }}
                      <span class="text-lg">{{
                        numberToFormat(
                          dangerAssetData.findVehicleAssetSum,
                          'Object',
                        ).unit
                      }}</span>
                    </div>
                  </div>

                  <v-img
                    v-if="$vuetify.theme.dark"
                    contain
                    src="@/assets/images/pages/line-up.png"
                  ></v-img>
                  <v-img
                    v-else
                    contain
                    src="@/assets/images/pages/line-up1.svg"
                  ></v-img>
                </v-card-text>
              </v-card>
            </v-skeleton-loader>
          </v-col>

          <v-col cols="12" md="3" sm="6">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <v-card class="pa-3">
                <v-card-text class="h-full text--primary">
                  <div class="d-flex justify-space-between mb-5 mt-n3">
                    <div class="d-flex justify-center align-center">
                      <vsoc-icon
                        class="mr-2"
                        color="reverse"
                        type="fill"
                        size="1.5rem"
                        icon="icon-gaojingjibiebiaozhi"
                      ></vsoc-icon>
                      <span class="text-xxl">{{
                        $t('analytics.totalAlerts')
                      }}</span>
                    </div>
                    <div class="text-n4xl font-weight-semibold">
                      {{
                        numberToFormat(dangerAssetData.findAlarmCount, 'Object')
                          .num
                      }}
                      <span class="text-lg">{{
                        numberToFormat(dangerAssetData.findAlarmCount, 'Object')
                          .unit
                      }}</span>
                    </div>
                  </div>
                  <v-img
                    v-if="$vuetify.theme.dark"
                    contain
                    src="@/assets/images/pages/line-middle.png"
                  ></v-img>
                  <v-img
                    v-else
                    contain
                    src="@/assets/images/pages/line-middle1.svg"
                  ></v-img>
                </v-card-text>
              </v-card>
            </v-skeleton-loader>
          </v-col>

          <v-col cols="12" md="3" sm="6">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <ring-card
                :title="$t('analytics.alertLevel')"
                :color-list="alertLevelOption.colorList"
                :list="alertLevelOption.list"
                echart-id="chart-stats-total-sales"
                :formatter="alertLevelOption.formatter"
                @click="event => onSearch(event, 'alarmLevelList')"
              ></ring-card>
            </v-skeleton-loader>
          </v-col>

          <v-col cols="12" md="3" sm="6">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <ring-card
                :title="$t('analytics.alertStatus')"
                :list="alertStatusOption.list"
                :color-list="alertStatusOption.colorList"
                :padding="[3, 5]"
                echart-id="alert-status-chart"
                :formatter="alertStatusOption.formatter"
                @click="event => onSearch(event, 'statusList')"
              ></ring-card>
            </v-skeleton-loader>
          </v-col>

          <v-col cols="12" md="6" sm="12">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading || this.influenceModelOption.isLoading"
              type="image"
              class="mx-auto w-100"
              style="height: 26.5rem"
            >
              <v-card class="d-flex flex-column h-100">
                <v-card-title class="align-start text-xxl">
                  {{ $t('analytics.affectedModel') }}
                </v-card-title>
                <v-card-text
                  class="flex-1 pb-0"
                  v-empty-chart="influenceModelOption.list.length === 0"
                >
                  <template v-if="influenceModelOption.list.length !== 0">
                    <v-row class="h-100">
                      <v-col cols="5" sm="6" class="h-100">
                        <vsoc-chart
                          echart-id="donut"
                          :option="influenceModelOption.chartOption"
                          @highlight="onHighlight"
                          @click="event => onSearch(event, 'vehicleModel')"
                        ></vsoc-chart>
                      </v-col>
                      <v-col
                        cols="7"
                        sm="6"
                        class="d-flex flex-column justify-center h-100"
                      >
                        <v-simple-table
                          class="car-table"
                          style="max-height: 20rem !important"
                          height="100%"
                        >
                          <template v-slot:default>
                            <tbody class="overflow-hidden">
                              <tr
                                v-for="item in influenceModelOption.list"
                                :key="item.dessert"
                              >
                                <td class="px-0">
                                  <v-icon
                                    class="mr-2"
                                    size="6px"
                                    :color="item.color"
                                  >
                                    mdi-circle-medium
                                  </v-icon>

                                  <span
                                    style="max-width: 10rem"
                                    class="text-overflow-hide inline-block align-middle text--primary"
                                    v-show-tips="item.name"
                                  >
                                    {{ item.name }}
                                  </span>
                                </td>
                                <td class="text-center text--primary pl-0">
                                  {{ item.value | numberToFormat }}
                                </td>
                                <td class="text-center text--secondary">
                                  {{ item.percent }}
                                </td>
                              </tr>
                            </tbody>
                          </template>
                        </v-simple-table>
                      </v-col>
                    </v-row>
                  </template>
                </v-card-text>
              </v-card>
            </v-skeleton-loader>
          </v-col>

          <v-col cols="12" md="6" sm="12">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              type="image"
              class="mx-auto h-full w-100"
              :loading="isLoading"
              min-height="26.5rem"
              style="height: 26.5rem"
            >
              <template v-slot:default>
                <v-card class="h-full d-flex flex-column">
                  <v-card-title class="align-start text-xxl">
                    <span>{{ $t('analytics.safeEventTop5') }}</span>
                  </v-card-title>

                  <v-card-text
                    class="flex-1 mt-2 pb-0"
                    v-empty-chart="totalEarning.length === 0"
                  >
                    <template v-if="totalEarning.length !== 0">
                      <div
                        v-for="(earning, index) in totalEarning"
                        :key="index"
                        class="d-flex flex-column cursor-pointer"
                        style="height: 20%"
                        @click="onSearch(earning, 'nameList')"
                      >
                        <div class="d-flex">
                          <div
                            class="text-overflow-hide text--primary"
                            style="max-width: calc(100% - 8.5rem)"
                          >
                            {{ earning.title }}
                          </div>
                          <v-spacer></v-spacer>

                          <p
                            class="text--primary font-weight-medium mb-2 text-right"
                          >
                            <span
                              >{{ earning.earning | numberToFormat }}
                              <strong class="ml-5 text--secondary"
                                >{{ earning.progress }}%</strong
                              >
                            </span>
                          </p>
                        </div>
                        <v-progress-linear
                          :value="earning.progress"
                          :color="earning.color"
                          background-color="bg-body"
                          rounded
                          :height="6 | getRoundSize"
                        ></v-progress-linear>
                      </div>
                    </template>
                  </v-card-text>
                </v-card>
              </template>
            </v-skeleton-loader>
          </v-col>

          <v-col cols="12" md="12" sm="12">
            <!-- <div id="TODO"></div> -->
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
              min-height="45rem"
            >
              <ecommerce-user-table
                :showAssetList="true"
                :list="affectAssetList"
                :advanceQuery="advanceQuery"
                :total="affectAssetTotal"
                @refresh="refreshPage"
              ></ecommerce-user-table>
            </v-skeleton-loader>
          </v-col>
        </v-row>
      </v-tab-item>
    </v-tabs-items>
    <div>
      <analytics-drawer
        ref="analyticsDrawer"
        v-model="showAdvanceSearch"
        :current-tab="userTab"
        @do-query="doQuery"
      ></analytics-drawer>
    </div>
  </div>
</template>

<script>
import {
  getFindAffectAsset,
  getRiskAssetAnalyse,
} from '@/api/analytics/index.js'
import ChartEmpty from '@/components/ChartEmpty.vue'
import VsocChart from '@/components/VsocChart'
import { numberToFormat, toDate } from '@/util/filters'
import PercentPie from '@/views/dashboards/analytics/charts-components/PercentPie'
import RingCard from '@/views/dashboards/analytics/charts-components/RingCard'
import { mdiCarMultiple } from '@mdi/js'
import themeConfig from '@themeConfig'
import { endOfDay, startOfDay, subDays } from 'date-fns'
import { orderBy } from 'lodash'
import VueApexCharts from 'vue-apexcharts'

import AnalyticsDrawer from './AnalyticsDrawer.vue'
import EcommerceUserTable from './EcommerceUserTable.vue'
import Refresh from './Refresh.vue'
import apexChatData from './charts-components/apexChartData'
export default {
  name: 'Analytics',
  components: {
    ChartEmpty,
    RingCard,
    VsocChart,
    VueApexCharts,
    EcommerceUserTable,
    PercentPie,
    AnalyticsDrawer,
    Refresh,
  },
  data() {
    return {
      numberToFormat,
      isMapLoading: true,
      isLoading: true,

      showAdvanceSearch: false,
      advanceQuery: {
        startDate: toDate(startOfDay(subDays(new Date(), 6))),
        endDate: toDate(endOfDay(new Date())),
        alarmLevelList: ['0', '1', '2', '3', '4'],
        statusList: ['0', '1', '2'],
        assetType: '', // 资产类型
        assetTypeList: [],
        detectorRange: ['0'], // 检测范围
        tagsList: [],
        vehicleId: '',
        vehicleYearList: [],
        vehicleEngineList: [],
        nameList: [],
        vehicleModel: '',
        vehicleVin: '',
        id: '',
        deviceType: '',
        pageNum: 1,
        pageSize: 10,
        assetRange: '0', // 0-单项资产，1-多项资产
      },
      dangerAssetData: {},

      apexChatData,
      page: 0,
      refreshDate: 0,

      queryDateRange: [],

      totalEarning: [],

      userTab: 0,

      alertCounting: {
        statTitle: '告警总数',
        icon: 'mdi-flash',
        color: 'primary',
        statistics: '362',
        change: '-18%',
      },

      alertLevelOption: {
        list: [],
        formatter: params => `{value|${params.percent}%}`,
      },
      alertStatusOption: {
        list: [],
        formatter: params => `{value|${params.percent}%}`,
      },

      // 影响车企
      influenceModelOption: {
        list: [],
        total: 0,
        chartOption: {},
        isLoading: false,
      },
      // 影响资产
      affectAssetList: [],
      affectAssetTotal: 0,
    }
  },
  watch: {
    '$vuetify.theme.dark': {
      handler(newVal) {
        if (
          this.influenceModelOption.chartOption.series &&
          this.influenceModelOption.chartOption.series.length > 0
        ) {
          this.influenceModelOption.chartOption.series[0].itemStyle = {
            borderWidth: 2,
            borderRadius: 16,
            borderColor: newVal
              ? themeConfig.themes.dark.backgroundColor
              : themeConfig.themes.light.backgroundColor,
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    tabs() {
      return [
        { icon: mdiCarMultiple, title: this.$t('analytics.tab1'), value: 0 },
      ]
    },
    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    alarmStatus() {
      return this.$store.state.enums.enums.AlarmStatus
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
  },

  async mounted() {
    sessionStorage.setItem('isRefreshed', 'true')
  },
  methods: {
    onShowDrawer() {
      this.$refs.analyticsDrawer.advanceQuery = this.advanceQuery
      this.showAdvanceSearch = true
    },

    onChangeTab() {
      Object.assign(
        this.$data.advanceQuery,
        this.$options.data.call(this).advanceQuery,
      )
      this.init()
    },
    onHighlight(event, myChart) {
      this.influenceModelOption.chartOption.tooltip.backgroundColor =
        event.color
      myChart.setOption(this.influenceModelOption.chartOption)
    },
    goAlert(item) {
      this.$router.push({
        name: '告警详情',
        query: { id: item.id, vehicleId: item.vehicleId },
      })
    },
    doQuery(params) {
      Object.assign(this.advanceQuery, params)
      this.init()
    },
    refreshPage(page, callback) {
      this.advanceQuery.pageNum = page.pageNum
      this.advanceQuery.pageSize = page.pageSize
      this.fetchFindAffectAsset(() => {
        callback && callback()
      })
    },

    //获取影响资产
    async fetchFindAffectAsset(callback) {
      try {
        const params = {
          startDate: this.advanceQuery.startDate,
          endDate: this.advanceQuery.endDate,
          alarmLevelList: this.advanceQuery.alarmLevelList,
          statusList: this.advanceQuery.statusList,
          assetType: this.advanceQuery.assetType,
          vehicleModel: this.advanceQuery.vehicleModel,
          nameList: this.advanceQuery.nameList,
          vehicleEngineList: this.advanceQuery.vehicleEngineList,
          vehicleYearList: this.advanceQuery.vehicleYearList,
          deviceType: this.advanceQuery.deviceType,
          pageNum: this.advanceQuery.pageNum,
          pageSize: this.advanceQuery.pageSize,
          assetRange: this.advanceQuery.assetRange,
        }
        const { data } = await getFindAffectAsset(params)
        this.affectAssetList = data.records
        this.affectAssetTotal = data.total
        callback && callback()
      } catch (err) {
        console.log('影响资产列表报错', err)
      } finally {
      }
    },
    // 获取危险资产分析
    async loadRiskAssetAnalyse() {
      try {
        const params = {
          startDate: this.advanceQuery.startDate,
          endDate: this.advanceQuery.endDate,
          alarmLevelList: this.advanceQuery.alarmLevelList,
          statusList: this.advanceQuery.statusList,
          assetType: this.advanceQuery.assetType,
          vehicleModel: this.advanceQuery.vehicleModel,
          nameList: this.advanceQuery.nameList,
          vehicleEngineList: this.advanceQuery.vehicleEngineList,
          vehicleYearList: this.advanceQuery.vehicleYearList,
          deviceType: this.advanceQuery.deviceType,
          pageNum: this.advanceQuery.pageNum,
          pageSize: this.advanceQuery.pageSize,
          assetRange: this.advanceQuery.assetRange,
        }
        const { data } = await getRiskAssetAnalyse(params)

        this.dangerAssetData = data

        // 告警级别数据
        this.alertLevelOption.list = data.findAlarmLevel.map(item => ({
          name: this.alertLevel[item.alarmLevel]?.text,
          value: item.count,
          color: this.alertLevel[item.alarmLevel]?.color,
          percent: item.format,
          level: item.alarmLevel,
        }))

        // 告警状态数据
        this.alertStatusOption.list = data.findAlarmStatus.map(item => ({
          name: this.alarmStatus[item.status]?.text,
          value: item.count,
          color: this.alarmStatus[item.status]?.color,
          percent: item.format,
          status: item.status,
        }))

        // 影响车企
        this.handlerInfluData(data)

        // 告警事件Top5
        this.totalEarning = data.safeEvent.map(item => ({
          ...item,
          title: item.name,
          color: this.alertLevel[item.alarmLevel]?.color,
          earning: item.count,
          progress: item.alarmPercentage.split('%')[0],
          level: item.alarmLevel,
        }))
      } catch (err) {
        console.log('威胁分析-危险资产分析报错', err)
      } finally {
        this.isLoading = false
      }
    },

    // 影响车企
    handlerInfluData(data) {
      this.influenceModelOption.isLoading = true
      // 影响车企数据
      this.influenceModelOption.total = data.influenceModel.length
      let baseColor = ['#533DF1', '#44E2FE', '#40CD6E', '#2F95E9', '#FBBA2C']
      let index = Math.ceil(this.influenceModelOption.total / 5)
      const colorList = new Array(index).fill(baseColor).flat()
      this.influenceModelOption.chartOption.color = colorList

      // 倒序
      const sortList = orderBy(data.influenceModel, 'count', 'desc')
      this.influenceModelOption.list = sortList.map((item, index) => ({
        name: item.vehicleModel,
        percent: item.format,
        value: item.count,
        color: colorList[index],
      }))
      this.influenceModelOption.chartOption = apexChatData.donutChart
      this.influenceModelOption.chartOption.series[0].data =
        this.influenceModelOption.list

      this.influenceModelOption.chartOption.title = {
        subtext: '{img| }\t\t{text|车企总数}',
        text: numberToFormat(this.influenceModelOption.total),
        top: '34%',
        left: '48.2%',
        textAlign: 'center',
        padding: [5, 5],
        textStyle: {
          fontSize: '4rem',
          // lineHeight: 78,
          fontWeight: 600,
        },
        subtextStyle: {
          rich: {
            text: {
              fontSize: 16,
              // lineHeight: 30,
              color: themeConfig.themes.dark.secondary,
              verticalAlign: 'bottom',
            },
            img: {
              width: 20,
              height: 20,
              backgroundColor: {
                image: require('../../../assets/images/pages/car.png'),
              },
            },
          },
        },
      }
      this.influenceModelOption.chartOption.color = colorList

      this.influenceModelOption.isLoading = false
    },
    async init() {
      this.refreshDate = new Date().getTime()
      this.isLoading = true
      // this.$showLoading()
      this.loadRiskAssetAnalyse()
      this.fetchFindAffectAsset()
    },
    refresh(obj) {
      // this.advanceQuery.startDate = obj.start
      // this.advanceQuery.endDate = obj.end
      this.advanceQuery = {
        ...this.advanceQuery,
        startDate: obj.start,
        endDate: obj.end,
      }
      this.$nextTick(() => {
        this.init()
      })
    },
    onSearch(event, queryKey) {
      // 重置参数
      Object.assign(
        this.$data.advanceQuery,
        this.$options.data.call(this).advanceQuery,
      )
      switch (queryKey) {
        case 'vehicleModel':
          this.advanceQuery.vehicleModel = event.name
          break
        case 'alarmLevelList':
          this.advanceQuery.alarmLevelList = [event.data.level]
          break
        case 'statusList':
          this.advanceQuery.statusList = [event.data.status]
          break
        case 'nameList':
          this.advanceQuery.nameList = [event.name]
        default:
          break
      }

      this.init()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .v-skeleton-loader__image {
  height: 100% !important;
}

.row--dense {
  > [class*='col-'] {
    padding: 0.4286rem;
  }
}

::v-deep
  .car-table.v-data-table
  > .v-data-table__wrapper
  > table
  > tbody
  > tr:not(:last-child)
  > td:not(.v-data-table__mobile-row) {
  border: none !important;
}

::v-deep .app-content-container {
  padding: 0;
}
</style>

<style lang="scss">
@import '@core/preset/preset/apps/user.scss';

#chart-sales-overview {
  .apexcharts-canvas {
    .apexcharts-text {
      &.apexcharts-datalabel-value {
        font-weight: 600;
      }
      &.apexcharts-datalabel-label {
        font-size: 1rem;
      }
    }
  }
}

.sales-overview-stats-table {
  width: 100%;
  td {
    padding-bottom: 1rem;
  }

  .stats-dot {
    padding: 0.33rem;
  }

  // Set opacity of dots
  tr {
    &:nth-of-type(1) {
      td:nth-of-type(2) {
        .stats-dot {
          opacity: 0.7;
        }
      }
    }
    &:nth-of-type(2) {
      td:nth-of-type(1) {
        .stats-dot {
          opacity: 0.5;
        }
      }
      td:nth-of-type(2) {
        .stats-dot {
          opacity: 0.15;
        }
      }
    }
  }
}

.v-application {
  &.theme--dark {
    #chart-stats-total-sales {
      path {
        stroke: #312d4b;
        filter: none;
      }
    }
  }
}

.chart-wrapper {
  max-width: 130px;
}
.statistics-table {
  border-top: solid 1px rgba(93, 89, 98, 0.14);
  .badge-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-radius: 50%;
    margin: 4px;
  }
}
</style>
