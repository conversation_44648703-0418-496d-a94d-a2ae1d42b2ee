<!-- 告警详情 -->
<template>
  <div class="ma-3 alert-detail">
    <div class="d-flex align-center mt-n1 mb-1">
      <v-btn class="mr-3" icon small @click="$router.go(-1)">
        <v-icon class="text--primary" size="20"> mdi-arrow-left </v-icon>
      </v-btn>
      <v-breadcrumbs class="pa-0" :items="breadItems">
        <!-- <template v-slot:item="{ item }">
          <v-breadcrumbs-item :href="item.href">
            {{ $generateMenuTitle(item.text) }}
          </v-breadcrumbs-item>
        </template> -->
      </v-breadcrumbs>
    </div>
    <v-row dense>
      <!-- 告警基本信息 -->
      <v-col cols="12">
        <v-row dense>
          <v-col cols="12" style="min-height: 146px">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image, card-heading"
              class="mx-auto h-full w-100"
            >
              <v-card class="h-full d-flex flex-column">
                <v-card-title
                  class="d-flex pb-0 align-center justify-space-between w-100"
                  style="padding-top: 20px"
                >
                  <div
                    class="d-flex align-center w-80"
                    style="
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                  >
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                          class="d-flex align-center justify-center text--primary tipsBox"
                          :style="{
                            backgroundColor: alertLevel[alertInfo.alarmLevel]
                              ? alertLevel[alertInfo.alarmLevel].color
                              : '',
                          }"
                        >
                          <vsoc-icon
                            type="fill"
                            icon="icon-gaojingjibiebiaozhi"
                            size="25px"
                            :style="{
                              color: $vuetify.theme.isDark ? '#171B34' : '#fff',
                            }"
                          ></vsoc-icon>
                        </div>
                      </template>
                      <span>{{
                        alertLevel[alertInfo.alarmLevel]
                          ? alertLevel[alertInfo.alarmLevel].text
                          : ''
                      }}</span>
                    </v-tooltip>
                    <div class="ps-10" style="width: 100%">
                      <div
                        class="d-flex w-100 align-center"
                        style="margin: 0 0 4px 15px"
                      >
                        <div class="d-flex align-center">
                          <vsoc-icon
                            type="fill"
                            class="mr-2 text-secondary"
                            icon="icon-TmpSVG"
                          ></vsoc-icon>
                          <div
                            v-show-tips
                            class="text-base-root font-weight-normal text-secondary"
                          >
                            {{ alertInfo.id }}
                          </div>
                        </div>

                        <div
                          v-show-tips
                          style="max-width: calc(100% - 80px)"
                          class="text-title font-weight-medium ml-3"
                        >
                          {{ alertInfo.name }}
                        </div>
                        <!-- 资产 -->
                        <!-- <div
                          v-if="assetTypeEnum[alertInfo.assetType]"
                          style="margin-left: 15px"
                        >
                          <v-chip small label class="bg-btn px-2">
                            <span class="text-content font-weight-medium">{{
                              assetTypeEnum[alertInfo.assetType].text
                            }}</span>
                          </v-chip>
                        </div> -->
                      </div>
                      <div
                        class="d-flex align-center"
                        style="
                          margin: 0 15px;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <!-- 车型 -->
                        <vsoc-icon
                          v-if="alertInfo.assetType"
                          type="fill"
                          class="mr-2 text-secondary"
                          :icon="assetTypeEnum[alertInfo.assetType].icon"
                        ></vsoc-icon>
                        <div
                          v-show-tips="
                            '影响资产' + '：' + (alertInfo.vehicleId || 'N/A')
                          "
                          class="text-base-root font-weight-normal text-secondary"
                          style="max-width: 150px"
                        >
                          {{ alertInfo.vehicleId || 'N/A' }}
                        </div>
                        <div class="line mx-4"></div>
                        <!-- 所属车企 -->
                        <div
                          v-show-tips="
                            $t('alert.headers.model') +
                            '：' +
                            (alertInfo.vehicleCompanyName || 'N/A')
                          "
                          class="text-base-root font-weight-normal text-secondary"
                          style="max-width: 150px"
                        >
                          {{ alertInfo.vehicleCompanyName || 'N/A' }}
                        </div>
                        <div class="line mx-4"></div>

                        <!-- 告警分类 -->
                        <div
                          v-show-tips="
                            $t('alert.headers.tag') +
                            '：' +
                            (alertInfo.warnClassifyName || 'N/A')
                          "
                          class="text-base-root font-weight-normal text-secondary"
                          style="max-width: 150px"
                        >
                          {{ alertInfo.warnClassifyName | dataFilter }}
                        </div>
                        <div class="line mx-4"></div>
                        <!-- 触发时间 -->
                        <vsoc-icon
                          type="fill"
                          class="mr-2 text-secondary"
                          icon="icon-shijian1"
                        ></vsoc-icon>
                        <div
                          v-show-tips
                          class="text-base-root font-weight-normal text-secondary"
                        >
                          <span class="mr-2">{{
                            $t('alert.headers.triggered')
                          }}</span>
                          {{ alertInfo.alarmDate | toDate }}
                        </div>
                        <div class="line mx-4"></div>
                        <!-- 最后更新时间 -->
                        <vsoc-icon
                          type="fill"
                          class="mr-2 text-secondary"
                          icon="icon-shijian1"
                        ></vsoc-icon>
                        <div
                          v-show-tips
                          class="text-base-root font-weight-normal text-secondary"
                        >
                          <span class="mr-2">{{
                            $t('alert.headers.update')
                          }}</span>
                          {{ alertInfo.updateDate | toDate }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="d-flex align-center w-20"
                    style="justify-content: flex-end"
                  >
                    <v-select
                      v-if="!isShowUpdate"
                      v-model="alertInfo.status"
                      dense
                      outlined
                      class="mr-4"
                      color="primary"
                      :menu-props="{ offsetY: true, maxHeight: 600 }"
                      append-icon="mdi-chevron-down"
                      hide-details="top"
                      :items="alertStatusList"
                      :label="$t('alert.btn.changeStatus')"
                      @change="$_changeStatus"
                    >
                      <template v-slot:item="{ item }">
                        <div class="d-flex" style="flex-direction: column">
                          <div
                            v-if="!item.disabled"
                            class="d-flex align-center py-3"
                          >
                            <span
                              class="status-color"
                              :style="{
                                background: alertStatus[item.value]
                                  ? alertStatus[item.value].color
                                  : '',
                              }"
                            ></span>
                            <span>
                              {{ item.text }}
                            </span>
                          </div>
                          <div v-else class="text-body py-3">
                            {{ item.text }}
                          </div>
                        </div>
                      </template>
                    </v-select>

                    <div
                      v-if="isShowUpdate"
                      :class="[
                        'mr-4',
                        'status-box',
                        $vuetify.theme.isDark ? 'status-2' : 'status-1',
                      ]"
                    >
                      <div
                        :style="{
                          backgroundColor: alertList[alertInfo.status]
                            ? alertList[alertInfo.status].color
                            : '',
                          borderRadius: '50%',
                          height: '6px',
                          width: '6px',
                          display: 'in',
                        }"
                      ></div>
                      <div class="text-content text-secondary ml-2">
                        {{
                          alertList[alertInfo.status]
                            ? alertList[alertInfo.status].text
                            : ''
                        }}
                      </div>
                    </div>
                    <v-menu
                      v-if="parentPermissions.indexOf('alert-allstatus') !== -1"
                      offset-y
                      left
                      nudge-bottom="4"
                      transition="scale-transition"
                      content-class="menu-list"
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          v-bind="attrs"
                          v-on="on"
                          dense
                          :color="$vuetify.theme.isDark ? '#222742' : '#F5F6FA'"
                          class="font-weight-bolder bg-body px-3"
                          elevation="0"
                          max-width="38"
                          max-height="38"
                        >
                          <vsoc-icon
                            type="fill"
                            icon="icon-gengduo"
                            size="20px"
                          ></vsoc-icon>
                        </v-btn>
                      </template>
                      <v-list>
                        <v-list-item
                          v-if="
                            parentPermissions.indexOf('alert-allstatus') !== -1
                          "
                          class="list-hover px-9"
                          @click="showUpdate"
                        >
                          <v-list-item-title>
                            {{ $t('alert.btn.status1') }}
                          </v-list-item-title>
                        </v-list-item>
                        <!-- <v-list-item
                          v-if="
                            alertInfo.assetRange === '0' &&
                            parentPermissions.indexOf('alert-result') !== -1
                          "
                          class="list-hover px-9"
                          @click="goAdvance"
                        >
                          <v-list-item-title>
                            {{ $t('alert.btn.advanced') }}
                          </v-list-item-title>
                        </v-list-item>
                        <v-list-item
                          v-if="
                            parentPermissions.indexOf('alert-ticket') !== -1
                          "
                          class="list-hover px-9"
                          @click="addTicket"
                        >
                          <v-list-item-title>
                            {{ $t('alert.btn.ticket') }}
                          </v-list-item-title>
                        </v-list-item>
                        <v-list-item
                          v-if="
                            parentPermissions.indexOf('alert-record') !== -1
                          "
                          class="list-hover px-9"
                          @click="checkRecord"
                        >
                          <v-list-item-title>
                            {{ $t('alert.btn.dispose') }}
                          </v-list-item-title>
                        </v-list-item>
                        <v-list-item
                          v-if="
                            parentPermissions.indexOf('alert-detection') !== -1
                          "
                          class="list-hover px-9"
                          @click="checkDetection"
                        >
                          <v-list-item-title>
                            {{ $t('alert.btn.detection') }}
                          </v-list-item-title>
                        </v-list-item> -->
                      </v-list>
                    </v-menu>
                  </div>
                </v-card-title>
                <v-card-text
                  v-if="!isShowUpdate"
                  class="flex-1 py-6 d-flex flex-column justify-space-between"
                >
                  <v-row class="pa-0 ma-0">
                    <v-col class="mb-0" cols="12">
                      <div class="wrapper-description ps-14">
                        <div
                          class="text text--primary text-content font-weight-semibold-light"
                        >
                          <div v-if="alertInfo.description" class="tag-wrap">
                            <v-btn
                              v-copy="alertInfo.description"
                              v-show-tips="$t('action.copy')"
                              icon
                              class="text--primary"
                              style="margin-top: -8px"
                            >
                              <vsoc-icon
                                type="fill"
                                size="16px"
                                icon="icon-fuzhi"
                              ></vsoc-icon>
                            </v-btn>
                          </div>
                          {{ alertInfo.description | dataFilter }}
                          <div
                            v-if="alertInfo.description"
                            class="tag-wrap-inline"
                          >
                            <v-btn
                              v-copy="alertInfo.description"
                              v-show-tips="$t('action.copy')"
                              icon
                              class="text--primary"
                            >
                              <vsoc-icon
                                type="fill"
                                size="16px"
                                icon="icon-fuzhi"
                              ></vsoc-icon>
                            </v-btn>
                          </div>
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </v-card-text>

                <!-- 告警变更 -->
                <template v-if="isShowUpdate">
                  <div
                    class="mt-6"
                    :style="{
                      borderTop: $vuetify.theme.isDark
                        ? '1px solid #2b3152'
                        : '1px solid #E6EAF2',
                    }"
                  />
                  <v-card-text
                    class="flex-1 pa-0 ma-0 d-flex flex-column justify-space-between"
                  >
                    <v-form ref="form" v-model="updateValid">
                      <div style="padding: 36px 0 10px 0">
                        <v-row class="ma-0 px-14 w-100">
                          <v-col class="mr-3">
                            <!-- :disabled="
                            !$store.state.permission.buttons.includes(
                            'alert-alarmchange', ) " -->
                            <v-select
                              dense
                              outlined
                              v-model="updateQuery.alarmLevel"
                              :menu-props="{ offsetY: true }"
                              :items="alertLevelList"
                              :label="$t('alert.headers.severity')"
                              clearable
                            >
                              <template v-slot:item="{ item }">
                                <div class="d-flex align-center text-center">
                                  <vsoc-icon
                                    type="fill"
                                    icon="icon-gaojingjibiebiaozhi"
                                    size="14px"
                                    :style="{ color: item.color }"
                                    class="mr-2"
                                  ></vsoc-icon>
                                  <span class="text-base color-base">
                                    {{ item.text }}
                                  </span>
                                </div>
                              </template>
                            </v-select>
                          </v-col>

                          <v-col class="mr-3">
                            <v-autocomplete
                              dense
                              outlined
                              v-model="updateQuery.warnClassifyId"
                              :menu-props="{ offsetY: true, maxHeight: 400 }"
                              :items="alertTags"
                              item-value="id"
                              item-text="name"
                              item-disabled="disabled"
                              :label="$t('alert.headers.tag')"
                              clearable
                            >
                            </v-autocomplete>
                          </v-col>

                          <v-col class="mr-3">
                            <v-select
                              dense
                              outlined
                              v-model="updateQuery.status"
                              :menu-props="{ offsetY: true, maxHeight: 600 }"
                              :items="alertStatusList"
                              :label="$t('alert.headers.status')"
                              clearable
                            >
                              <template v-slot:item="{ item }">
                                <div
                                  class="d-flex"
                                  style="flex-direction: column"
                                >
                                  <div
                                    v-if="!item.disabled"
                                    class="d-flex align-center py-3"
                                  >
                                    <span
                                      class="status-color"
                                      :style="{
                                        background: alertStatus[item.value]
                                          ? alertStatus[item.value].color
                                          : '',
                                      }"
                                    ></span>
                                    <span>
                                      {{ item.text }}
                                    </span>
                                  </div>
                                  <div v-else class="text-body py-3">
                                    {{ item.text }}
                                  </div>
                                </div>
                              </template>
                            </v-select>
                          </v-col>
                          <div class="d-flex align-top">
                            <v-btn
                              class="me-3"
                              depressed
                              color="primary"
                              @click="onUpdate()"
                              >{{ $t('action.save') }}</v-btn
                            >
                            <v-btn
                              outlined
                              color="secondary"
                              @click="isShowUpdate = false"
                              >{{ $t('action.cancel') }}</v-btn
                            >
                          </div>
                        </v-row>
                      </div>
                    </v-form>
                  </v-card-text>
                </template>
              </v-card>
            </v-skeleton-loader>
          </v-col>
          <!-- 最近6个月告警信息统计 -->
          <v-col cols="6" style="min-height: 20rem; padding-right: 10px">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image, card-heading"
              class="mx-auto h-full w-100"
            >
              <v-card class="h-full">
                <v-card-text class="h-100">
                  <vsoc-chart
                    echartId="lastSixChart"
                    :option="chartOption"
                    :isYDashed="true"
                  ></vsoc-chart>
                </v-card-text>
              </v-card>
            </v-skeleton-loader>
          </v-col>
          <!-- 最近24小时告警信息统计 -->
          <v-col cols="6">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image, card-heading"
              class="mx-auto h-full w-100"
            >
              <v-card class="h-full d-flex flex-column">
                <v-card-title class="text-title">
                  {{ $t('alert.detail.last24Hours') }}
                </v-card-title>
                <v-card-text class="flex-1 d-flex justify-space-between">
                  <div class="d-flex text-center">
                    <div class="w-100 d-flex">
                      <div
                        style="font-size: 36px; line-height: 44px"
                        class="mr-3 text--primary font-weight-semibold-light"
                      >
                        {{ alarmStatisticsOfTheLastDay }}
                      </div>
                      <div class="d-flex align-center justify-center">
                        <vsoc-icon
                          type="fill"
                          icon="icon-gaojingjibiebiaozhi"
                          class="mr-3 text-danger"
                          size="16px"
                        ></vsoc-icon>
                        <span class="text-content">{{
                          $t('alert.detail.occurrences')
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="d-flex text-center">
                    <div class="w-100 d-flex">
                      <div class="mr-3 d-flex align-center justify-center">
                        <vsoc-icon
                          type="fill"
                          :icon="
                            assetTypeEnum[alertInfo.assetType] &&
                            assetTypeEnum[alertInfo.assetType].icon
                          "
                          class="mr-3 text-primary"
                          size="16px"
                        ></vsoc-icon>
                        <span class="text-content">{{
                          $t('alert.detail.assets')
                        }}</span>
                      </div>
                      <div
                        style="font-size: 36px; line-height: 44px"
                        class="text--primary font-weight-semibold-light"
                      >
                        {{ vehicleAssetStatisticsOfTheLastDay }}
                      </div>
                    </div>
                  </div>
                </v-card-text>
                <img
                  style="padding: 1rem 1.25rem"
                  src="@/assets/images/pages/<EMAIL>"
                  height="124px"
                />
              </v-card>
            </v-skeleton-loader>
          </v-col>
        </v-row>
      </v-col>

      <!--车企基本信息-->
      <v-col cols="12" style="min-height: 10rem">
        <v-skeleton-loader
          :dark="$vuetify.theme.dark"
          :loading="isLoading"
          type="image, card-heading"
          class="mx-auto h-full w-100"
        >
          <v-card class="h-full">
            <v-card-title class="text-title"> 车企基本信息 </v-card-title>
            <v-card-text>
              <div class="d-flex justify-space-between">
                <div>
                  <div class="d-flex align-center">
                    <span class="text-base-root"> 影响车企 </span>
                  </div>
                  <div
                    v-show-tips
                    style="max-width: 200px"
                    class="text-content text--primary d-flex align-center mt-2"
                  >
                    {{ assetsInfo.automakerName || '-' }}
                  </div>
                </div>
                <div>
                  <div class="text-base-root">
                    {{ $t('alert.detail.posture') }}
                  </div>
                  <div class="mt-2">
                    <v-chip
                      label
                      small
                      :text-color="
                        getRiskLevelInfo(
                          assetsInfo.riskScore,
                          assetsInfo.isSupervise,
                          assetsInfo.riskdetails,
                        ).color
                      "
                      class="opacity-b1 px-3 chip-box"
                    >
                      <v-icon
                        :color="
                          getRiskLevelInfo(
                            assetsInfo.riskScore,
                            assetsInfo.isSupervise,
                            assetsInfo.riskdetails,
                          ).color
                        "
                        size="16px"
                      >
                        mdi-shield
                      </v-icon>
                      <span class="pl-1 text-base-root">{{
                        getRiskLevelInfo(
                          assetsInfo.riskScore,
                          assetsInfo.isSupervise,
                          assetsInfo.riskdetails,
                        ).text
                      }}</span>
                    </v-chip>
                  </div>
                </div>

                <div>
                  <div class="text-base-root">风险评分</div>
                  <div
                    class="text--primary mt-2 text-content font-weight-semibold-light"
                  >
                    {{ assetsInfo.riskScore || '-' }}
                  </div>
                </div>
                <div>
                  <div class="text-base-root">
                    {{ $t('alert.detail.alerts') }}
                  </div>
                  <div v-if="assetsInfo.totalNum > 0" class="d-flex mt-1">
                    <template v-for="(alarmItem, index) in assetsInfo.riskList">
                      <v-avatar
                        v-if="alarmItem.value > 0"
                        class="mr-2 cursor-pointer"
                        size="28px"
                        :key="index"
                        :color="alertLevel[alarmItem.alarmLevel].color"
                        @click="goAlertList(alarmItem)"
                      >
                        <span
                          v-show-tips="
                            alertLevel[alarmItem.alarmLevel].text +
                            ':' +
                            alarmItem.value
                          "
                          class="text-content text-no-wrap white--text"
                          >{{ alarmItem.value | tranNumber(1) }}</span
                        >
                      </v-avatar>
                    </template>
                  </div>
                  <div class="mt-2" v-else-if="assetsInfo.is_supervise === '1'">
                    -
                  </div>
                  <div class="mt-2" v-else>暂无风险</div>
                </div>

                <div>
                  <div class="text-base-root">所在地</div>
                  <div
                    v-show-tips="assetsInfo.address"
                    style="max-width: 300px"
                    class="text--primary text-overflow-hide mt-2 text-content"
                  >
                    {{ assetsInfo.address || '-' }}
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-skeleton-loader>
      </v-col>

      <v-col v-if="isLoading" cols="12" style="min-height: 50rem">
        <v-skeleton-loader
          :dark="$vuetify.theme.dark"
          :loading="isLoading"
          type="image, card-heading"
          class="mx-auto h-full w-100"
        >
        </v-skeleton-loader>
      </v-col>

      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="text-title">资产事件轴</div>
            <v-btn
              elevation="0"
              class="bg-body ml-3"
              x-small
              v-show-tips="$t('action.refresh')"
              :loading="isLoading"
              @click="onReload"
            >
              <vsoc-icon
                size="1.5rem"
                class="primary--text"
                type="fill"
                icon="icon-shuaxin"
              ></vsoc-icon>
            </v-btn>
          </v-card-title>
          <v-card-text>
            <div class="mt-4">
              <asset-time-line
                ref="timeLine"
                time-line-type="alert1"
                :time-line-data="timeLineData"
                :isNoData="!timeLineData.length"
                @change="timeLineChange"
              ></asset-time-line>
            </div>
            <!-- 事件详情 -->
            <asset-event-detail
              ref="eventDetails"
              :data="currentLineData"
              type="alert"
              :assetType="assetsInfo.assetType"
            ></asset-event-detail>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import {
  alarmTypeStatistics,
  getAlerts,
  multiAssetDetail,
  updateStatus,
} from '@/api/alert'
import {
  addRelationByAssetId,
  getVehicleDetails,
  getVin,
} from '@/api/asset/index'
import {
  addAutomaker,
  editAutomaker,
  getAutomakerDetail,
} from '@/api/asset/automaker'
import { generateEventName } from '@/util/filters'

import VsocDialog from '@/components/VsocDialog.vue'
import { toDate } from '@/util/filters'
import { deepClone } from '@/util/utils'

import { grid, legend, tooltip } from '@/assets/echarts-theme/constant'
import VsocChart from '@/components/VsocChart'
import VsocDrawer from '@/components/VsocDrawer.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import VsocAutocomplete from '@/components/vsoc-autocomplete/vsoc-autocomplete.vue'
import AssetEventDetail from '@/views/asset/asset-info/components/AssetEventDetail.vue'
import AssetTimeLine from '@/views/asset/asset-info/components/AssetTimeLine.vue'
import { format, subMonths } from 'date-fns'

import imgUrlDark from '@/assets/images/misc/label_dark.png'
import imgUrlLight from '@/assets/images/misc/label_light.png'
import { setLocalStorage } from '@/util/localStorage'
import { getAutomakerSecurityLevelUnified } from '@/util/algorithm'
export default {
  name: 'AlertDetail',
  components: {
    // AlertMap,
    AssetTimeLine,
    AssetEventDetail,
    VsocChart,
    VsocDialog,
    VsocPagination,
    VsocDrawer,
    VsocAutocomplete,
  },
  watch: {},
  data() {
    return {
      imgUrlLight,
      imgUrlDark,
      center: {},
      isLoading: false,
      alertList: [],
      updateQuery: {
        alarmLevel: '',
        warnClassifyId: '',
        status: '',
      },
      alertTags: [],
      updateValid: true,
      isShowUpdate: false,
      isOverlay: false,
      query: {
        pageNum: 1,
        pageSize: 5,
      },

      tableData: [],
      tableDataTotal: 0,
      tableLoading: false,
      isDialogVisible: false,
      // 告警基本信息
      alertInfo: {
        alarmLevel: '',
        warnClassifyName: '',
      },
      loading: true,

      // 最近24小时发生次数量
      alarmStatisticsOfTheLastDay: '',

      // 最近24小时影响资产数量
      vehicleAssetStatisticsOfTheLastDay: '',

      // 当前时间
      currentDate: new Date(),
      alertName: '',
      // 资产信息
      assetsInfo: {},
      address: '',

      timeLineData: [],
      currentLineData: {
        fullDigitalTwin: [],
      },
      localList: [],

      alertChart: {
        alertNum: [],
        vehicleNum: [],
      },
      parentPermissions: [],
      last6updateDate: '',
    }
  },

  computed: {
    mapEnabledStatus() {
      return this.$store.getters['enums/getMapEnabledStatus']
    },
    alertStatusList() {
      let alertList = deepClone(this.$store.getters['enums/getAlertStatus'])
      this.alertList = deepClone(alertList)
      this.alertList.splice(
        alertList.findIndex(v => v.value === '0'),
        0,
      )
      this.alertList.splice(
        alertList.findIndex(v => v.value === '2'),
        0,
      )
      alertList.splice(
        alertList.findIndex(v => v.value === '0'),
        0,
        {
          text: this.$t('alert.headers.unhandledAlertStatus'),
          value: '-1',
          disabled: true,
        },
      )
      alertList.splice(
        alertList.findIndex(v => v.value === '2'),
        0,
        {
          text: this.$t('alert.headers.handledAlertStatus'),
          value: '-2',
          disabled: true,
        },
      )
      return alertList
    },
    headers() {
      return [
        {
          text: this.$t('alert.headers.asset'),
          value: 'id',
          width: '220px',
        },
        {
          text: this.$t('alert.headers.vin'),
          value: 'vin',
          sortable: false,
          width: '160px',
        },
        {
          text: this.$t('alert.headers.model'),
          value: 'model',
          width: '140px',
        },
        {
          text: this.$t('alert.headers.occurrenceTime'),
          value: 'occurrenceTime',
          width: '180px',
        },
        {
          text: '',
          value: 'actions',
          sortable: false,
          width: '120px',
        },
      ]
    },
    chartOption() {
      let xData = []
      for (let i = 0; i < 6; i++) {
        xData.push(format(subMonths(new Date(), i), 'yyyy-MM'))
      }
      let list = []
      for (const key in this.alertChart) {
        if (key === 'alertNum') {
          list.push({
            name: this.$t('alert.detail.occurrences'),
            data: this.alertChart[key].map(v => v.y),
          })
        }
        if (key === 'vehicleNum') {
          list.push({
            name: this.$t('alert.detail.assets'),
            data: this.alertChart[key].map(v => v.y),
          })
        }
      }
      let color = this.$vuetify.theme.themes.dark.primary
      if (!this.$vuetify.theme.isDark) {
        color = this.$vuetify.theme.themes.light.primary
      }
      const colorList = ['#DA1F1F', color]
      return {
        color: colorList,
        title: {
          show: true,
          text: this.$t('alert.detail.last6Months'),
          subtext: this.$t('alert.hint.detailTip1', [this.last6updateDate]),
          itemGap: 12,
          textStyle: {
            fontSize: 16,
            // fontWeight: '500',
          },
        },
        series: list.map((item, index) => {
          // return { ...seriesCommon, ...item }
          return {
            name: item.name,
            data: item.data,
            type: 'bar',
            barWidth: 6,
            barGap: 1,

            itemStyle: {
              borderRadius: 10,
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false,
                colorStops: [
                  { offset: 0, color: colorList[index] },
                  { offset: 1, color: colorList[index] },
                ],
              },
            },
          }
        }),
        tooltip,
        legend,
        grid: {
          ...grid,
          top: 60,
          // right: 30,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            data: xData,
            axisLine: { show: false },
            axisTick: { show: false },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
      }
    },

    assetOption() {
      let list = [
        {
          name: this.$t('alert.headers.year'),
          data: this.assetsInfo.assetStatisticsByYear.map(v => v.y),
        },
      ]
      const colorList = ['#DA1F1F']
      return {
        color: colorList,
        title: {
          show: true,
          text: this.$t('alert.headers.year'),
          textStyle: {
            fontSize: 16,
            // fontWeight: '500',
          },
        },
        series: list.map((item, index) => {
          return {
            name: item.name,
            data: item.data,
            type: 'bar',
            barWidth: 6,
            barGap: 1,

            itemStyle: {
              borderRadius: 10,
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false,
                colorStops: [{ offset: 0, color: colorList[index] }],
              },
            },
          }
        }),
        tooltip,
        legend,
        grid: {
          ...grid,
          top: 60,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            data:
              this.assetsInfo.assetStatisticsByYear &&
              this.assetsInfo.assetStatisticsByYear.map(v => v.x),
            axisLine: { show: false },
            axisTick: { show: false },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
      }
    },

    breadItems() {
      const routerList = this.$router.getRoutes()
      let first = routerList.find(
        v => v.name === this.$route.meta.navActiveLink,
      )
      let second = routerList.find(v => v.name === '告警详情')
      return [
        {
          text: this.$generateMenuTitle(first.meta),
          disabled: true,
          href: '#',
        },
        {
          text: this.$generateMenuTitle(second.meta),
          disabled: false,
          href: '#',
        },
      ]
    },
    carStatusMap() {
      return this.$store.state.enums.enums.HealthStatus
    },
    addressInfo() {
      return this.address
    },
    alertStatus() {
      return this.$store.state.enums.enums.AlarmStatus
    },
    // 获取告警等级颜色
    alertLevelList() {
      return Object.assign([], this.$store.state.enums.enums.AlarmLevel)
    },

    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    vehicleStatus() {
      return this.$store.state.enums.enums.HealthStatus
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
    deviceTypeMap() {
      return this.$store.state.enums.enums.DeviceType
    },
    alertTypes() {
      return this.$store.getters['enums/getAlertType']
    },
  },
  created() {
    this.init('init')
    this.parentPermissions =
      this.$router
        .getRoutes()
        .find(v => v.meta.id === this.$route.meta.parentId)?.meta.buttons || []
  },
  beforeDestroy() {},
  methods: {
    init(mode) {
      //获取告警分类
      this.getAllAlertTags()
      //获取告警信息
      this.getAllInfo(mode)
    },
    //刷新
    onReload() {
      this.$nextTick(() => {
        this.init('refresh')
      })
    },
    //获取告警分类
    async getAllAlertTags() {
      try {
        const data = await this.$store.dispatch('global/loadClassifyList', {
          type: '0',
        })
        this.alertTags =
          data.map(v => {
            return {
              ...v,
              disabled: v.status === '1' ? true : false,
            }
          }) || []
      } catch (error) {
        console.error(`获取告警分类错误：${error}`)
      }
    },
    showUpdate() {
      this.isShowUpdate = true
      this.updateQuery.alarmLevel = this.alertInfo.alarmLevel
      this.updateQuery.status = this.alertInfo.status
      this.updateQuery.warnClassifyId = this.alertInfo.warnClassifyId || ''
    },
    onUpdate(callback) {
      this.$swal({
        title: this.$t('alert.btn.status1'),
        text: this.$t('alert.hint.updateTip1', [this.alertInfo.id]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        customClass: {
          confirmButton: 'sweet-btn-primary',
          container: 'container-box',
        },
        allowOutsideClick: false,
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const params = Object.assign(this.updateQuery, {
              idList: [this.alertInfo.id],
            })
            const res = await updateStatus(params)
            if (res.code === 200) {
              this.$notify.info('success', this.$t('alert.hint.update'))
            }
            this.getAllInfo()
            this.isShowUpdate = false
            callback && callback()
          } catch (e) {
            callback(false, true)
            console.error(`变更状态错误：${e}`)
          }
        } else {
          callback(false, true)
          setTimeout(() => {
            this.isShowUpdate = true
          })
        }
      })
    },
    goAlertList() {
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          vehicleCompanyCode: this.assetsInfo.automakerCode,
          statusList: JSON.stringify(['0', '1']),
          alarmLevelList: JSON.stringify(['0', '1', '2', '3', '4', '5']),
        },
      })
    },
    //跳转查看结果
    checkRecord() {
      this.$router.push(`/record?alarmId=${this.alertInfo.id}`)
    },
    checkDetection() {
      let allButtons = this.$store.state.permission.buttons || []
      if (allButtons.includes('detector-edit')) {
        this.$router.push(
          `/detection/detect/edit?id=${this.alertInfo.detectorId}`,
        )
      } else {
        this.$router.push(
          `/detection/detect/detail?id=${this.alertInfo.detectorId}&isDetail=1`,
        )
      }
    },
    //转工单
    addTicket() {
      const params = {
        priority: this.alertInfo.alarmLevel,
        title: this.alertInfo.name,
        ticketContent: this.alertInfo.description,
        dataSource: '2',
        relationId: this.alertInfo.id,
        affectModelList: this.alertInfo.vehicleModel,
      }
      this.$router.push('/ticket/addTicket?type=1')
    },

    async getAllInfo(type) {
      if (type === 'init') {
        this.isLoading = true
      }
      try {
        getAlerts({ alarmId: this.$route.query.id })
          .then(res => {
            this.alertInfo = res.data.records.length && res.data.records[0]
            this.getStatic()
            this.getModel()
            this.getTimeLine()
          })
          .catch(() => {
            this.isLoading = false
          })
      } catch (e) {
      } finally {
      }
    },
    // 获取风险评级信息（使用告警数据模式）
    getRiskLevelInfo(riskScore, isSupervise, riskDetails = null) {
      // 如果未监管，返回默认的未监管状态
      if (isSupervise !== '0') {
        return {
          color: '#B4BBCC',
          text: '-',
          level: 'Unsupervised',
        }
      }
      return getAutomakerSecurityLevelUnified({
        riskDetails: riskDetails,
        riskScore: riskScore,
      })
    },
    //获取车企
    async getModel() {
      await getAutomakerDetail({
        automakerCode: this.alertInfo.vehicleCompanyCode,
      })
        .then(res => {
          this.isLoading = false
          this.assetsInfo = res.data

          this.assetsInfo.totalNum = Object.values(
            this.assetsInfo.riskDetails,
          ).reduce((acc, current) => acc + current, 0)

          this.assetsInfo.riskList = [
            {
              value: this.assetsInfo.riskDetails.critical,
              alarmLevel: '0',
            },
            {
              value: this.assetsInfo.riskDetails.high,
              alarmLevel: '1',
            },
            {
              value: this.assetsInfo.riskDetails.medium,
              alarmLevel: '2',
            },
            {
              value: this.assetsInfo.riskDetails.low,
              alarmLevel: '3',
            },
            {
              value: this.assetsInfo.riskDetails.unassigned,
              alarmLevel: '4',
            },
          ]
          // this.center = res.data.corePoint
          // this.assetsInfo.vehicleGroups = this.assetsInfo.groupsInfoList.map(
          //   item => item.name,
          // )
          // if (this.assetsInfo.eventAxisVos.length > 0) {
          //   const newArr = this.assetsInfo.eventAxisVos.length > 0 && [
          //     ...this.assetsInfo.eventAxisVos,
          //   ]
          //   this.handleTimeLine(newArr, this.alertInfo.vehicleId)
          // }
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    //获取统计数
    async getStatic() {
      await alarmTypeStatistics({
        id: this.$route.query.id,
        name: this.alertInfo.name,
      })
        .then(v => {
          this.isLoading = false
          if (v.data) {
            this.last6updateDate = v.data.updateDate
            this.alarmStatisticsOfTheLastDay =
              v.data.alarmStatisticsOfTheLastDay || 0
            this.vehicleAssetStatisticsOfTheLastDay =
              v.data.vehicleAssetStatisticsOfTheLastDay || 0
            this.alertChart = {
              alertNum: v.data.alarmStatisticsInTheLastSixMonths.reverse(),
              vehicleNum:
                v.data.vehicleAssetStatisticsInTheLastSixMonths.reverse(),
            }
            this.currentDate = new Date().getTime()
          }
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    //获取资产时间轴
    async getTimeLine() {
      getVehicleDetails({
        id: this.alertInfo.vehicleId,
        alarmId: this.$route.query.id,
        alarmStatusList: ['0', '1', '2', '3', '4', '5'],
      })
        .then(v => {
          this.isLoading = false
          if (v.data.eventAxisVos.length > 0) {
            const newArr = v.data.eventAxisVos.length > 0 && [
              ...v.data.eventAxisVos,
            ]
            this.handleTimeLine(newArr, this.alertInfo.vehicleId)
          }
        })
        .catch(() => {
          this.isLoading = false
        })
    },

    // 处理资产时间线
    handleTimeLine(data, assetsId) {
      const color =
        this.alertLevel[this.alertInfo.alarmLevel]?.color || this.$alertColor[2]
      const timeLineData = data.map(item => {
        let latitude = item.latitude
        let longitude = item.longitude
        if (item.fullDigitalTwin && item.fullDigitalTwin.length) {
          item.fullDigitalTwin.forEach(v => {
            if (v.id === 'vehicle_location_currentlocation_latitude') {
              latitude = v.value
            }
            if (v.id === 'vehicle_location_currentlocation_longitude') {
              longitude = v.value
            }
          })
        }

        return {
          ...item,
          fullDigitalTwin: item.fullDigitalTwin || [],
          color,
          assetsId,
          latitude,
          longitude,
        }
      })
      this.timeLineData = timeLineData
      this.currentLineData = this.timeLineData[this.timeLineData.length - 1]
    },

    $_changeStatus(val) {
      if (this.parentPermissions.indexOf('alert-status') === -1) return
      updateStatus({
        status: val,
        idList: [this.alertInfo.id],
      })
        .then(res => {
          if (res.code === 200) {
            this.$notify.info('success', this.$t('alert.hint.update'))
          }
        })
        .catch(() => {})
    },

    goAdvance() {
      const query = {
        alertId: this.alertInfo.detectorId,
        vehicleId: this.alertInfo.vehicleId,
        assetType: this.alertInfo.assetType,
        searchDateTime: this.alertInfo.alarmDate,
      }
      let alarmSignal = this.timeLineData.find(v => v.type === 'Alarm')
      if (alarmSignal) {
        const originalArr = [
          ...alarmSignal.specialFields,
          ...alarmSignal.defaultFields,
        ]
        if (originalArr && originalArr.length) {
          query.fullDigitalTwin = JSON.stringify(originalArr)
        }
      }
      this.$router.push({
        path: '/investigate',
        query: query,
      })
      // this.$router.push(
      //   `/investigate?alertId=${this.alertInfo.detectorId}&vehicleId=${this.alertInfo.vehicleId}&assetType=${this.alertInfo.assetType}&searchDateTime=${this.alertInfo.alarmDate}&`,
      // )
    },

    timeLineChange(i) {
      this.currentLineData = {
        ...this.timeLineData[i],
        isRecentActivity: i === this.timeLineData.length - 1,
      }
      // this.$refs.map &&
      //   this.$refs.map.flyTo(
      //     this.currentLineData.longitude,
      //     this.currentLineData.latitude,
      //   )
      // this.currentLineData = this.timeLineData[i]
    },
  },
}
</script>

<style lang="scss" scoped>
.alert-handle-btn {
  width: 34px !important;
  height: 34px !important;
}
.tipsBox {
  width: 48px;
  height: 48px;
  border-radius: 0 5px 5px 0;
  position: absolute;
  left: 0px;
}
::v-deep .list-hover:hover {
  color: var(--v-primary-base) !important;
}
.lotus .list-hover:hover {
  color: #726d06 !important;
}
.alert-detail {
  ::v-deep .v-badge--dot .v-badge__badge {
    top: -2px !important;
  }
  .status-box {
    display: flex;
    align-items: center;
    padding: 8px 12px 8px 16px;
    height: 38px;
    background-size: cover;
    background-repeat: no-repeat;
  }
  .status-1 {
    background-image: url('../../../assets/images/misc/label_light.png');
  }
  .status-2 {
    background-image: url('../../../assets/images/misc/label_dark.png');
  }
  .text-title {
    font-size: 16px !important;
  }
  .alert-info {
    word-break: break-all;
    text-wrap: wrap;
    white-space: normal !important;
    text-align: justify;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }
  .line {
    height: 14px;
    width: 1px;
    background: var(--v-bgDivider-base);
  }
  .v-card .v-card__title {
    font-size: 16px !important;
  }
  .text-base-root {
    font-size: 12px !important;
  }

  .row,
  .col {
    margin: 0;
    padding: 0;
  }
  .col-12 {
    margin: 0 0 10px 0;
  }

  .alert-right-margin {
    margin-right: 10px;
  }
}

.status-color {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  margin: 0 16px;
}
.new-sub-title-flag {
  // content: ' ';
  width: 0.2rem;
  height: 1.5rem;
  position: absolute;
  margin-top: 0.25rem;
  margin-left: -0.25rem;
  background: $primary;
  border-radius: 0.1rem;
}

.text-danger {
  color: #e53935;
}

.wrapper-description {
  display: flex;
  overflow: hidden;
  .text {
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: justify;
    position: relative;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }
  .text::before {
    content: '';
    height: calc(100% - 30px);
    float: right;
  }
  .text::after {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    background: var(--v-backgroundColor-base);
  }
  .tag-wrap {
    position: relative;
    float: right;
    clear: both;
  }
  .tag-wrap-inline {
    display: inline-block;
  }

  .tag-wrap::before {
    content: '';
    position: absolute;
    // left: -5px;
    color: var(--v-color-base);
    transform: translateX(-100%);
  }
}
</style>
