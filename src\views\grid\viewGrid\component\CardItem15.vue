<template>
  <!-- 条形图 上下布局 -->
  <div class="box">
    <div
      :ref="'ref' + itemKey"
      class="position-relative big-content-box box-line-3"
    >
      <div ref="boxHeader" class="box-header">
        {{ title }}
        <!-- <span v-if="itemKey === 'C053'" class="box-header-num">{{
          numberToFormat(allTotal)
        }}</span> -->
      </div>

      <img
        v-if="itemKey === 'C002'"
        class="position-absolute top-image"
        src="../images/top11.svg"
      />

      <div class="tab-content tab-content-1 mt-0 h-100">
        <template v-if="list.length !== 0">
          <vue-seamless-scroll
            ref="seamlessScroll"
            :data="list"
            :class-option="classOption"
            style="overflow: hidden"
            class="h-100"
          >
            <div ref="boxContent">
              <div
                class="d-flex align-center w-100 list-item"
                v-for="(item, index) in list"
                :key="index"
              >
                <div
                  v-if="['C010', 'C002'].includes(itemKey)"
                  class="dot-1 text-center"
                  :style="{
                    background: getLevelColor(item).color,
                  }"
                >
                  {{ index + 1 }}
                </div>
                <div
                  v-else
                  class="dot align-start"
                  :style="{
                    background:
                      itemKey === 'C007'
                        ? getClassificationColor(item.classificationLevel)
                        : getLevelColor(item).color,
                  }"
                ></div>
                <div class="flex-1 ml-4">
                  <div class="d-flex justify-space-between align-start">
                    <div>
                      <div class="risk-name fs-14-1 d-flex align-center">
                        <div>
                          {{
                            itemKey === 'C007'
                              ? item.platformName
                              : item.automakerName
                          }}
                        </div>
                        <div
                          v-if="itemKey === 'C010'"
                          class="cursor-pointer ml-2"
                          @click.stop="jumpPage(item)"
                        >
                          <vsoc-icon
                            class="fs-12"
                            type="fill"
                            icon="icon-tiaozhuan"
                          ></vsoc-icon>
                        </div>
                      </div>
                      <!-- <div v-if="itemKey === 'C053'" class="fs-12">
                      {{ item.riskSafetyIndex }}%
                    </div> -->
                    </div>
                    <div class="color--primary fs-14">
                      {{
                        itemKey === 'C007'
                          ? item.platformCode
                          : numberToFormat(item.riskScore)
                      }}
                    </div>
                  </div>
                  <!-- 进度条 -->
                  <v-progress-linear
                    background-color="transparent"
                    :color="
                      itemKey === 'C007'
                        ? getClassificationColor(item.classificationLevel)
                        : getLevelColor(item).color
                    "
                    :value="itemKey === 'C007' ? 100 : item.riskPercentage"
                    class="progress-linear progress-linear-15"
                  >
                  </v-progress-linear>
                </div>
              </div>
            </div>
          </vue-seamless-scroll>
        </template>
        <template v-else>
          <div class="h-full">
            <div
              class="fs-16 color--primary h-100 d-flex justify-center align-center"
            >
              None
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { getClassificationColor } from '../util/defaultPreview'
import { getAutomakerSecurityLevelUnified } from '@/util/algorithm'

export default {
  name: 'CardItem15',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    allCanvs: {
      type: Array,
      default: () => {
        return []
      },
    },
    total: {
      type: [Number, String],
      default: 0,
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    itemKey: {
      type: String,
      default: '',
    },
  },
  created() {},
  data() {
    return {
      numberToFormat,
      classOption: {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 10000,
        hoverStop: true, // 是否开启鼠标悬停stop
      },
    }
  },
  computed: {
    allTotal() {
      return this.list.reduce((acc, item) => {
        return acc + (item.riskSafetyCount || 0)
      }, 0)
    },
  },
  components: {
    vueSeamlessScroll,
  },
  mounted() {
    this.getBoxHeight()
    window.addEventListener('resize', this.getBoxHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getBoxHeight)
  },
  methods: {
    // 获取定级级别颜色
    getClassificationColor,
    getLevelColor(item) {
      return getAutomakerSecurityLevelUnified({
        riskDetails: item.riskDetails,
        riskScore: item.riskScore,
      })
    },
    jumpPage(item) {
      let findItem = this.allCanvs.find(v => v.backGroundImg === '3')
      if (findItem) {
        this.$router.push(
          `/grid/shareCanvas?id=${findItem.id}&currentModelType=${item.automakerCode}`,
        )
      }
    },
    getBoxHeight() {
      setTimeout(() => {
        let boxLineHeight =
          this.$refs['ref' + this.itemKey]?.offsetHeight -
          this.$refs.boxHeader.offsetHeight -
          8
        let boxContentHeight = this.$refs.boxContent?.offsetHeight

        this.$refs.seamlessScroll && this.$refs.seamlessScroll.reset()
        if (boxLineHeight < boxContentHeight) {
          this.classOption.limitMoveNum = this.list.length
        } else {
          this.classOption.limitMoveNum = 10000
        }
      }, 300)
    },
  },
}
</script>
<style lang="scss">
// .data-view-1 .progress-linear-15 .v-progress-linear__determinate {
//   border-color: linear-gradient(
//     92.38deg,
//     #0f3480 0.9%,
//     #224fa9 99.43%
//   ) !important;
//   background: linear-gradient(
//     92.38deg,
//     #0f3480 0.9%,
//     #224fa9 99.43%
//   ) !important;
// }
</style>
