<!-- 告警抽屉 -->
<template>
  <vsoc-drawer
    :title="$t('action.advanced')"
    :width="400"
    :value="value"
    style="z-index: 9999999999999"
    @input="close"
    @click:close="close"
    @click:confirm="doQuery"
    @click:cancel="close"
  >
    <template #right-title>
      <v-btn icon @click="clearAdvanceQuery">
        <v-icon size="16">mdi-filter-variant</v-icon>
      </v-btn>
    </template>
    <template>
      <div class="text-lg font-weight-semibold mb-4">
        {{ $t('analytics.drawer.alertModel') }}
      </div>

      <v-combobox
        v-model="advanceQuery.vehicleModel"
        outlined
        dense
        color="primary"
        :items="modelItems"
        :label="$t('alert.headers.model')"
      ></v-combobox>

      <div class="text-lg font-weight-semibold mb-4">
        {{ $t('analytics.drawer.alertType') }}
      </div>
      <v-combobox
        v-model="advanceQuery.nameList"
        :menu-props="{ offsetY: true, maxHeight: 300 }"
        :items="alertTypes"
        :label="$t('alert.headers.type')"
        multiple
        outlined
        dense
        item-value="name"
        :search-input.sync="searchTag"
        :return-object="false"
      >
        <template v-slot:selection="{ item, index }">
          <v-chip
            small
            pill
            color="primary"
            close
            @click:close="removeTypes(index)"
          >
            {{ alertTypeMap[item] ? alertTypeMap[item].text : item }}
          </v-chip>
        </template>
        <template v-slot:no-data>
          <p
            class="pa-2 mb-0 text-body list-hover"
            @click="createType(searchTag)"
          >
            {{ $t('alert.hint.tip') }}：<strong>{{ searchTag }}</strong>
          </p>
        </template>
      </v-combobox>

      <div class="text-lg font-weight-semibold mt-4 mb-2">
        {{ $t('global.assetType') }}
      </div>
      <v-btn-toggle
        class="w-100"
        v-model="advanceQuery.assetType"
        color="primary"
      >
        <v-btn
          v-for="item in assetTypeEnum"
          :key="item.value + 'assetType'"
          :value="item.value"
          class="w-50"
          elevation="0"
        >
          <vsoc-icon type="fill" :icon="item.icon" class="mr-2"></vsoc-icon>
          <span>{{ item.text }}</span>
        </v-btn>
      </v-btn-toggle>

      <div class="text-lg font-weight-semibold mt-4 mb-2">
        {{ $t('analytics.alertLevel') }}
      </div>
      <div class="align-center flex-wrap mx-2">
        <!-- <div class="d-flex align-center flex-wrap"> -->
        <v-checkbox
          v-for="(item, i) in alertLevel"
          :key="i"
          v-model="advanceQuery.alarmLevelList"
          color="primary"
          :value="item.value"
          column
          hide-details
          class="mr-8 my-0"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <v-icon :style="{ color: item.color }" size="2.5rem">
                mdi-circle-medium
              </v-icon>
              <span class="text-sm font-weight-semibold">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
      <template v-if="currentTab === 1">
        <div class="text-lg font-weight-semibold my-4">
          {{ $t('analytics.drawer.detectionRange') }}
        </div>
        <div class="align-center flex-wrap mx-2">
          <v-checkbox
            v-for="(item, i) in extentEnum"
            :key="i"
            v-model="advanceQuery.detectorRange"
            color="primary"
            :value="item.value"
            column
            hide-details
            class="mr-8 my-1"
          >
            <template v-slot:label>
              <div class="d-flex align-center text-center">
                <v-icon color="primary" class="iconfont" left size="1.5rem">
                  {{ item.icon }}
                </v-icon>
                <span class="text-sm font-weight-semibold">
                  {{ item.text }}
                </span>
              </div>
            </template>
          </v-checkbox>
        </div>

        <!-- <div class="text-lg font-weight-semibold my-4">
          {{ $t('global.assetType') }}
        </div> -->

        <!-- <div class="align-center flex-wrap mx-2">
          <v-checkbox
            v-for="(item, i) in assetTypeEnum"
            :key="i"
            v-model="advanceQuery.assetTypeList"
            color="primary"
            :value="item.value"
            column
            hide-details
            class="mr-8 my-1"
          >
            <template v-slot:label>
              <div class="d-flex align-center text-center">
               <v-icon left size="1rem">
                  {{ item.icon }}
                </v-icon>
                <vsoc-icon left size="1.25rem" :icon="item.icon"></vsoc-icon>
                <span class="text-sm font-weight-semibold ml-2">
                  {{ item.text }}
                </span>
              </div>
            </template>
          </v-checkbox>
        </div> -->
      </template>

      <div class="text-lg font-weight-semibold mt-4 mb-2">
        {{ $t('analytics.alertStatus') }}
      </div>
      <!-- label="告警状态" -->
      <div class="align-center flex-wrap mx-2">
        <v-checkbox
          v-for="(item, i) in alertStatus"
          :key="i"
          v-model="advanceQuery.statusList"
          color="primary"
          :value="item.value"
          column
          hide-details
          class="mr-8 my-0"
          :disabled="item.disabled"
        >
          <template v-slot:label>
            <div
              v-if="$toItem(alertStatus, item.value)"
              class="d-flex align-center text-center text-no-wrap"
            >
              <v-icon size="2.3rem" :color="item.color">
                mdi-circle-medium
              </v-icon>
              <span class="text-sm font-weight-semibold">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>

      <template v-if="currentTab === 1">
        <div class="text-lg font-weight-semibold my-4">
          {{ $t('analytics.drawer.assetId') }}
        </div>
        <v-text-field
          v-model="advanceQuery.vehicleId"
          outlined
          dense
          color="primary"
        ></v-text-field>
        <div class="text-lg font-weight-semibold mb-4">
          {{ $t('analytics.drawer.alertId') }}
        </div>
        <v-text-field
          v-model="advanceQuery.id"
          outlined
          dense
          color="primary"
          hide-details
          class="mb-4"
        ></v-text-field>
      </template>
      <!-- 车辆年份 -->
      <!-- 屏蔽车辆年份的筛选条件 @Henry 20241204-->
      <div class="text-lg font-weight-semibold mt-4 mb-2" v-if="1 === 2">
        {{ $t('analytics.drawer.vehicleYear') }}
      </div>

      <div class="align-center flex-wrap mx-2" v-if="1 === 2">
        <v-checkbox
          v-for="(item, i) in yearList"
          :key="i"
          v-model="advanceQuery.vehicleYearList"
          color="primary"
          :value="item"
          hide-details
          class="mr-8 my-2"
        >
          <template v-slot:label>
            <div class="d-flex align-center">
              <span class="pl-2 text-sm font-weight-semibold">
                {{ item }}
              </span>
            </div>
          </template>
        </v-checkbox>
        <v-text-field
          v-model="advanceQuery.otherVehicleYearStr"
          outlined
          dense
          placeholder="eg:2018,2019"
          color="primary"
          :label="$t('analytics.drawer.otherYear')"
        ></v-text-field>
      </div>
      <!-- 车辆引擎类型 -->
      <!-- 屏蔽引擎类型的筛选条件 @Henry 20241204 -->
      <template v-if="currentTab === 1 && 1 === 2">
        <div class="text-lg font-weight-semibold mb-2">
          {{ $t('analytics.drawer.engineType') }}
        </div>
        <div class="align-center flex-wrap mx-2">
          <v-checkbox
            v-for="(item, i) in engineTypeList"
            :key="i"
            v-model="advanceQuery.vehicleEngineList"
            color="primary"
            :value="item"
            hide-details
            class="mr-8 my-2"
          >
            <template v-slot:label>
              <div class="d-flex align-center">
                <span class="pl-2 text-sm font-weight-semibold">
                  {{ item }}
                </span>
              </div>
            </template>
          </v-checkbox>
        </div>
      </template>
    </template>
    <!-- <template v-else>
      <div class="text-lg font-weight-semibold my-4">告警类型</div>
      <v-select
        v-model="dangerAdvanceQuery.nameList"
        color="primary"
        :items="alertTypes"
        label="告警类型"
        multiple
        outlined
        dense
        :menu-props="{ offsetY: true }"
      >
        <template v-slot:selection="{ index }">
          <span v-if="index === 0" class="text-caption">
            已选择：{{ dangerAdvanceQuery.nameList.length }}
          </span>
        </template>
      </v-select>
      <div class="text-lg font-weight-semibold mb-4">告警车型</div>
      <v-text-field
        v-model="dangerAdvanceQuery.vehicleModel"
        outlined
        dense
        color="primary"
        label="告警车型"
      ></v-text-field>
      <div class="text-lg font-weight-semibold mb-4">告警级别</div>
      <div class="d-flex align-center flex-wrap">
        <v-checkbox
          v-for="(item, i) in alertLevel"
          :key="i"
          v-model="dangerAdvanceQuery.alarmLevelList"
          color="primary"
          :value="item.value"
          hide-details
          class="mt-0 mr-8 mb-4"
        >
          <template v-slot:label>
            <div class="d-flex align-center text-center">
              <v-icon :style="{ color: item.color }" width="24" height="24">
                mdi-shield
              </v-icon>
              <span class="text-sm font-weight-semibold">
                {{ item.text }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
      <div class="text-lg font-weight-semibold mb-4">告警状态</div>
      <v-select
        v-model="dangerAdvanceQuery.statusList"
        outlined
        multiple
        color="primary"
        :menu-props="{ offsetY: true, maxHeight: 600 }"
        hide-details
        :items="alertStatusList"
        label="告警状态"
        chips
      >
        <template v-slot:item="{ item }">
          <div class="d-flex" style="flex-direction: column">
            <div v-if="!item.disabled" class="d-flex align-center py-3">
              <span
                class="status-color"
                :style="{ background: alertStatus[item.value].color }"
              ></span>
              <span class="text--primary">
                {{ item.text }}
              </span>
            </div>
            <div v-else class="text-body py-3">
              {{ item.text }}
            </div>
          </div>
        </template>
        <template v-slot:selection="{ item, index }">
          <v-chip
            label
            color="primary"
            close
            class="text-white"
            @click:close="removeAlertStatus(index)"
          >
            <span class="text-caption ls-0">{{ item.text }}</span>
          </v-chip>
        </template>
      </v-select>
      <div class="text-lg font-weight-semibold mb-2">车辆年份</div>
      <div class="d-flex align-center flex-wrap">
        <v-checkbox
          v-for="(item, i) in yearList"
          :key="i"
          v-model="dangerAdvanceQuery.vehicleYearList"
          color="primary"
          :value="item"
          hide-details
          class="mt-0 mr-4 mb-4"
        >
          <template v-slot:label>
            <div class="d-flex align-center">
              <span class="pl-2 text-sm font-weight-semibold">
                {{ item }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
      <div class="text-lg font-weight-semibold mb-2">引擎类型</div>
      <div class="d-flex align-center flex-wrap">
        <v-checkbox
          v-for="(item, i) in engineTypeList"
          :key="i"
          v-model="dangerAdvanceQuery.vehicleEngineList"
          color="primary"
          :value="item"
          hide-details
          class="mt-0 mr-4 mb-4"
        >
          <template v-slot:label>
            <div class="d-flex align-center">
              <span class="pl-2 text-sm font-weight-semibold">
                {{ item }}
              </span>
            </div>
          </template>
        </v-checkbox>
      </div>
    </template> -->
  </vsoc-drawer>
</template>

<script>
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'

// import VsocDateRange from '@/components/VsocDateRange.vue'
// import { getData } from '@/api/profile'
import VsocDrawer from '@/components/VsocDrawer.vue'
import { PAGESIZE_MAX } from '@/util/constant'
import { deepClone } from '@/util/utils'

export default {
  name: 'AnalyticsDrawer',
  components: {
    VsocDrawer,
    VsocDateRange,
  },
  props: {
    value: Boolean,
    alertTags: Array,
    currentTab: Number,
  },
  data() {
    return {
      createing: false,
      searchTag: null,
      modelItems: [],
      alertTypes: [],

      advanceQuery: {
        alarmLevelList: [],
        statusList: [],
        tagsList: [],
        vehicleId: '', // 资产id
        vehicleYearList: [],
        otherVehicleYearStr: '', // 其他年份
        vehicleEngineList: [],
        detectorRange: [], // 检测范围
        nameList: [],
        deviceType: '',
        vehicleModel: '',
        vehicleVin: '',
        id: '',
        assetType: '', // 资产类型
        assetTypeList: [],
        assetRange: '0', // 0-单项资产，1-多项资产
      },

      // alertAdvanceQuery: {
      //   alarmLevelList: [],
      //   statusList: [],
      //   tagsList: [],
      //   vehicleId: '',
      //   vehicleYearList: [],
      //   vehicleEngineList: [],
      //   nameList: [],
      //   vehicleModel: '',
      //   vehicleVin: '',
      //   id: '',
      // },
      // 危险资产分析
      dangerAdvanceQuery: {
        // startDate: '2000-12-26 06:00:27',
        // endDate: '2030-12-29 06:00:27',
        alarmLevelList: [],
        statusList: [],
        vehicleModel: '',
        deviceType: '',
        nameList: [],
        vehicleEngineList: [],
        vehicleYearList: [],
      },
      engineTypeList: ['燃油', '纯电', '混动', '插混', '未知'],
      yearList: ['2022', '2021', '2020', '2019', '2018'],
    }
  },
  watch: {
    value(newVal) {
      if (!newVal) return
      const allYearList = this.advanceQuery.vehicleYearList
      this.advanceQuery.otherVehicleYearStr = allYearList
        .filter(v => !this.yearList.includes(v))
        ?.join(',')
      this.advanceQuery.vehicleYearList = allYearList.filter(v =>
        this.yearList.includes(v),
      )
    },
  },
  computed: {
    // 告警状态
    // alertStatusList() {
    //   return this.$store.getters['enums/getAlertStatus']
    // },
    deviceTypeMap() {
      // return this.$store.state.enums.enums.DeviceType
      return Object.assign([], this.$store.state.enums.enums.DeviceType)
    },
    alertStatus() {
      return this.$store.getters['enums/getAlertStatusWithTitle']
    },

    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    extentEnum() {
      return this.$store.state.enums.enums.DetectorExtent
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
  },
  created() {
    this.init()
    this.loadAlertTypeData()
    this.loadAllAutomaker()
  },
  methods: {
    removeTypes(index) {
      this.advanceQuery.nameList.splice(index, 1)
    },
    async createType(tagName) {
      if (this.createing) return
      this.createing = true
      this.advanceQuery.nameList.push(tagName)
      this.searchTag = ''
      this.createing = false
    },
    //获取车企
    async loadAllAutomaker() {
      const data = await this.$store.dispatch('global/loadAllAutomaker')
      this.modelItems = data.map(v => v.name)
    },
    init() {
      const date = new Date()

      // 最近五年
      this.yearList = [
        date.getFullYear().toString(),
        (date.getFullYear() - 1).toString(),
        (date.getFullYear() - 2).toString(),
        (date.getFullYear() - 3).toString(),
        (date.getFullYear() - 4).toString(),
      ]
      this.advanceQuery.alarmLevelList = ['0', '1', '2', '3', '4']
      this.advanceQuery.statusList = ['0', '1', '2']
      this.handlerData()
    },

    // 查询告警类型数据
    async loadAlertTypeData() {
      try {
        // const { data } = await getData({
        //   pageNum: 1,
        //   pageSize: PAGESIZE_MAX,
        // })
        // this.alertTypes = data.records.map(v => ({
        //   text: v.name,
        //   value: v.id,
        // }))
        const obj = {}
        // this.alertTypes.forEach(item => {
        //   obj[item.value] = {
        //     text: item.text,
        //   }
        // })
        this.alertTypeMap = obj
      } catch (e) {
        console.error(`获取告警类型数据：${e}`)
      }
    },

    close(bool) {
      if (!bool) {
        this.$emit('input', false)
      }
    },

    setModel(val) {
      this.advanceQuery = val
    },

    closeChip(index) {
      console.log(index)
    },

    clearAdvanceQuery() {
      this.advanceQuery = {
        alarmLevelList: ['0', '1', '2', '3', '4'],
        statusList: ['0', '1', '2'],
        tagsList: [],
        vehicleId: '',
        vehicleYearList: [],
        otherVehicleYearStr: '',
        vehicleEngineList: [],
        detectorRange: ['0'],
        nameList: [],
        vehicleModel: '',
        deviceType: '',
        vehicleVin: '',
        id: '',
        assetType: '', // 资产类型
        assetRange: '0', // 0-单项资产，1-多项资产
      }

      // this.init()
    },

    //  高级查询
    doQuery(callback) {
      this.handlerData()
      callback()
    },
    handlerData() {
      const params = deepClone(this.advanceQuery)

      // 避免[""]
      const arr = this.advanceQuery.otherVehicleYearStr
        ?.split(',')
        ?.filter(v => !!v)
      delete params.otherVehicleYearStr
      params.vehicleYearList = this.advanceQuery.vehicleYearList.concat(arr)

      this.$emit('do-query', params)
    },
    open() {},

    removeAlertStatus(i) {
      this.advanceQuery.statusList.splice(i, 1)
    },

    removeTag(i) {
      this.advanceQuery.tagsList.splice(i, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.status-color {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  margin: 0 16px;
}
::v-deep .advanceQuery-box .v-input--radio-group__input {
  flex-direction: column !important;
}
</style>
