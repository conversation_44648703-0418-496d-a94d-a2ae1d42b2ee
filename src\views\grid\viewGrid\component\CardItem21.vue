<template>
  <!-- 条形轮播图不滚动 -->
  <div class="box">
    <div class="big-content-box box-line-3">
      <div class="box-header">
        {{ title }}
      </div>

      <div class="h-100" style="margin-top: 1%">
        <template v-if="list.length !== 0">
          <div class="h-100">
            <div
              class="d-flex align-center w-100"
              v-for="(item, index) in list"
              :key="index"
              style="height: 21.1%"
            >
              <div
                class="dot-1 text-center"
                :style="{
                  background: '#44e2fe',
                }"
              >
                {{ index + 1 }}
              </div>
              <div class="flex-1 ml-4">
                <div class="d-flex justify-space-between align-start">
                  <div>
                    <div class="risk-name fs-14-1 d-flex align-center">
                      <div>
                        {{ item.platformName }}
                      </div>
                    </div>
                  </div>
                  <div class="color--primary fs-14">
                    {{ numberToFormat(item.riskScore) }}
                  </div>
                </div>
                <!-- 进度条 -->
                <v-progress-linear
                  background-color="transparent"
                  color="#44e2fe"
                  :value="item.riskPercentage"
                  class="progress-linear progress-linear-15"
                >
                </v-progress-linear>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="h-full">
            <div
              class="fs-16 color--primary h-100 d-flex justify-center align-center"
            >
              None
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import { getClassificationColor } from '../util/defaultPreview'
export default {
  name: 'CardItem21',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    total: {
      type: [Number, String],
      default: 0,
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    itemKey: {
      type: String,
      default: '',
    },
  },
  created() {},
  data() {
    return {
      numberToFormat,
    }
  },
  computed: {
    allTotal() {
      return this.list.reduce((acc, item) => {
        return acc + (item.riskSafetyCount || 0)
      }, 0)
    },
  },
  components: {},
  mounted() {},

  methods: {
    getClassificationColor,
  },
}
</script>
<style lang="scss"></style>
