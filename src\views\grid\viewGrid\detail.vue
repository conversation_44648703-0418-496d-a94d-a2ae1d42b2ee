<template>
  <div class="data-view-5">
    <div class="data-view-1" v-resize="getScreenSize">
      <dv-loading v-if="isLoading">Loading...</dv-loading>
      <div
        class="dv-full-screen-container-1"
        :style="{
          backgroundImage:
            canvasItem.backgroundImage === '3'
              ? `url(${imgs[1]})`
              : `url(${imgs[0]})`,
        }"
        v-else
      >
        <div
          class="main-layout main-layout-1"
          style="height: calc(100vh - 0.2%)"
        >
          <div class="main-header">
            <div class="title-date d-flex align-center">
              <span>{{ dateYear }}</span
              ><span class="ml-1">{{ dateDay }}</span
              ><span class="ml-1"> {{ dateWeek }}</span>
            </div>

            <div
              v-if="canvasItem.backgroundImage !== '3'"
              class="title-date1 title-date2 d-flex align-center font-weight-semibold"
            >
              安徽省通信管理局
            </div>
            <div
              v-if="canvasItem.backgroundImage === '3'"
              class="title-date1 d-flex align-center"
            >
              已运营<span class="title-header-day mx-2">{{ continueDay }}</span
              ><span>{{ $t('global.time.day1') }}</span>
            </div>
            <dv-decoration-8
              @click="goIndex"
              class="header-left-decoration mh-high"
              :color="[primary, primary]"
            />

            <dv-decoration-5
              class="header-center-decoration"
              :color="[primary, primary]"
            />
            <dv-decoration-8
              class="header-right-decoration"
              :reverse="true"
              :color="[primary, primary]"
            />
            <div class="mh-title" @click="goIndex">
              {{ lang === 'en' ? canvasItem.titleEnName : canvasItem.title }}
            </div>

            <div
              style="right: 3%"
              class="mh-date justify-end d-flex align-center"
            >
              <!-- <span class="text-center"
              >{{ dateYear }} <v-spacer></v-spacer> {{ dateDay }}
              {{ dateWeek }}</span
            > -->

              <div
                v-if="canvasItem.backgroundImage !== '3'"
                class="title-tab title-date2 d-flex align-center font-weight-semibold"
              >
                <div
                  v-for="(item, index) in titleList"
                  :key="index"
                  :class="[
                    'h-100',
                    'cursor-pointer',
                    item.value === canvasItem.backgroundImage
                      ? 'title-tab-text'
                      : '',
                    index === 0 ? 'title-tab-text-1' : '',
                  ]"
                  @click="jumpPage(item)"
                >
                  {{ item.text }}
                </div>
              </div>

              <!-- <v-combobox
                v-model="currentTypeList"
                dense
                hide-details
                append-icon="mdi-chevron-down"
                class="date-combox title-margin"
                :items="vehicleType"
                item-text="text"
                item-value="value"
                :menu-props="{ offsetY: true }"
                @blur="onSelectData"
                @change="changeVehicles"
                :return-object="false"
                item-color="#44e2fe"
              >
                <template v-slot:selection="{ index }">
                  <span v-if="index === 0" class="check-text fs-14-1">
                    {{ $t('global.selected') }}{{ currentTypeList.length }}
                  </span>
                </template>
              </v-combobox> -->

              <v-menu
                v-if="canvasItem.backgroundImage === '3'"
                offset-y
                left
                nudge-bottom="5"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    elevation="0"
                    small
                    v-bind="attrs"
                    class="d-flex align-center text-base date-option title-margin"
                    v-on="on"
                  >
                    <template>
                      <div class="d-flex align-center">
                        <!-- <img
                          v-if="currentModeItem && currentModeItem.pictureCode"
                          :src="currentModeItem.pictureCode"
                          :width="$getCeilSize(16)"
                          class="mr-3"
                        /> -->
                        <span
                          class="fs-14-1 title-date2 font-weight-semibold"
                          style="color: #fff"
                        >
                          {{ currentModeItem ? currentModeItem.text : '' }}
                        </span>
                        <v-icon class="ml-3">mdi-chevron-down</v-icon>
                      </div>
                    </template>
                  </v-btn>
                </template>
                <v-list class="pa-0 model-list">
                  <v-list-item-group
                    mandatory
                    :value="currentModelType"
                    @change="changeVehicles"
                  >
                    <v-list-item
                      v-for="item in vehicleType"
                      :key="item.value"
                      :value="item.value"
                    >
                      <v-list-item-title class="fs-14-1 d-flex align-center">
                        <span>{{ item.text }}</span>
                      </v-list-item-title>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-menu>

              <v-menu
                v-if="canvasItem.backgroundImage !== '1'"
                offset-y
                left
                nudge-bottom="5"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    elevation="0"
                    small
                    v-bind="attrs"
                    class="d-flex align-center text-base date-option title-margin"
                    v-on="on"
                  >
                    <template>
                      <span class="fs-14-1">{{ currentText }}</span>
                      <v-icon class="ml-1">mdi-chevron-down</v-icon>
                    </template>
                  </v-btn>
                </template>
                <v-list class="pa-0 model-list">
                  <!-- mandatory -->
                  <v-list-item-group
                    mandatory
                    :value="currentDate"
                    @change="onSelectData"
                  >
                    <v-list-item
                      v-for="item in dateOption"
                      :key="item.value"
                      :value="item.value"
                    >
                      <v-list-item-title class="fs-14-1">{{
                        item.text
                      }}</v-list-item-title>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-menu>

              <!-- <v-menu offset-y left nudge-bottom="5">
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  elevation="0"
                  small
                  v-bind="attrs"
                  class="d-flex align-center text-base date-option ml-8"
                  v-on="on"
                >
                  <template>
                    <span class="fs-14-1">{{ currentTypeText }}</span>
                    <v-icon class="ml-1">mdi-chevron-down</v-icon>
                  </template>
                </v-btn>
              </template>
              <v-list class="pa-0 model-list">
                <v-list-item-group
                  multiple
                  mandatory
                  :value="currentTypeList"
                  @change="onSelectData"
                >
                  <v-list-item
                    v-for="item in vehicleType"
                    :key="item.id"
                    :value="item.id"
                  >
                    <v-list-item-title class="fs-14-1">{{
                      item.name
                    }}</v-list-item-title>
                  </v-list-item>
                </v-list-item-group>
              </v-list>
            </v-menu> -->
              <!-- <v-btn
                v-if="canvasItem.backgroundImage === '1'"
                icon
                @click.stop="jumpPage"
                class="title-margin"
              >
                <vsoc-icon
                  :icon="'icon-tiaozhuan'"
                  class="color--primary tianzhuan-icon"
                  type="fill"
                ></vsoc-icon>
              </v-btn> -->
              <v-btn class="title-margin" icon @click="toggleFullScreen">
                <vsoc-icon
                  v-if="!isFull && !isFullScreen"
                  :size="$getCeilSize(30)"
                  :icon="'icon-daxiaopingqiehuan-1'"
                ></vsoc-icon>
                <v-icon v-else :size="$getCeilSize(30)" :color="primary"
                  >mdi-power</v-icon
                >
              </v-btn>
              <!-- <v-btn
                v-if="!$route.query.token"
                class="mr-0"
                icon
                @click="shareScreen"
              >
                <vsoc-icon
                  :size="$getCeilSize(30)"
                  :icon="'icon-fenxiang'"
                ></vsoc-icon>
              </v-btn> -->
            </div>
          </div>

          <div class="main-container">
            <div ref="layout-box" class="layout-box" style="height: 100%">
              <grid-layout
                :layout="layoutData"
                :col-num="layoutColNum"
                :row-height="rowHeight"
                :minRows="minRows"
                :is-draggable="false"
                :is-resizable="false"
                :is-mirrored="false"
                :vertical-compact="true"
                :margin="[marginBoder, marginBoder]"
                :use-css-transforms="true"
                @layout-updated="layoutUpdatedEvent"
                @layout-ready="layoutReadyEvent"
              >
                <grid-item
                  v-for="item in layoutData"
                  :x="item.x"
                  :y="item.y"
                  :w="item.w"
                  :h="item.h"
                  :i="item.i"
                  :minW="item.minW"
                  :minH="item.minH"
                  :key="item.i"
                  @resized="resizedEvent"
                >
                  <!-- 定制化图形*1 -->
                  <!-- <card-border
                  v-if="item.chartValue === '0'"
                  :ref="'comp' + item.i"
                  :isBorder="item.isBorder"
                  :showDel="showDel"
                  @deleteCard="deleteCard(item.i)"
                  :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                >
                  <card-item1
                    :title="lang === 'en' ? item.itemEnName : item.itemName"
                    :list="item.value"
                  ></card-item1>
                </card-border> -->
                  <!-- 定制化态势 -->
                  <card-border
                    v-if="item.chartValue === '11'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <!-- :refreshDate="item.value.time"
                      :posture="item.value.posture" -->
                    <card-item12
                      :currentModelType="currentModelType"
                      :vehicleType="vehicleType"
                    ></card-item12>
                  </card-border>
                  <!-- 定制化图形*2 -->
                  <card-border
                    v-if="item.chartValue === '1'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item2
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item2>
                  </card-border>
                  <!-- 定制化图形*3 -->
                  <card-border
                    v-if="item.chartValue === '2'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item3
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item3>
                  </card-border>
                  <!-- 定制化图形*4 -->
                  <card-border
                    v-if="item.chartValue === '3'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item4
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                      :headers1="item.headers1"
                      :itemKey="item.itemKey"
                    ></card-item4>
                  </card-border>

                  <!-- 条形图1 -->
                  <card-border
                    v-if="item.chartValue === '4'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item5
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item5>
                  </card-border>

                  <!-- 条形图2 -->
                  <card-border
                    v-if="item.chartValue === '5'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item6
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.value.total"
                      :list="item.value.list"
                    ></card-item6>
                  </card-border>

                  <!-- 折线图-基础面积图 -->
                  <card-border
                    v-if="item.chartValue === '6'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <!-- :xList="xList" -->
                    <card-item7
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item7>
                  </card-border>

                  <!-- 折线图-基础平滑折线图 -->
                  <card-border
                    v-if="item.chartValue === '7'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item8
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.value.total"
                      :list="item.value.list"
                    ></card-item8>
                  </card-border>

                  <!-- 空心环形图1 -->
                  <card-border
                    v-if="item.chartValue === '8'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item9
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :modelImgs="modelImgs"
                    ></card-item9>
                  </card-border>

                  <!-- 空心环形图2 -->
                  <card-border
                    v-if="item.chartValue === '9'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item10
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.itemKey === 'C024' ? '' : item.value.total"
                      :list="
                        item.itemKey === 'C024' ? item.value : item.value.list
                      "
                      :isFull="!isFull && !isFullScreen"
                    ></card-item10>
                  </card-border>

                  <!-- 定制化Map -->
                  <card-border
                    v-if="item.chartValue === '10'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item11
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :toData="item.value"
                      :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                      :width="item.w * colWidth + (item.w - 1) * marginBoder"
                    ></card-item11>
                  </card-border>

                  <!-- 环形图 -->
                  <card-border
                    v-if="item.chartValue === '13'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item13
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                    ></card-item13>
                  </card-border>

                  <!-- 安徽地图 -->
                  <card-border
                    v-if="item.chartValue === '14'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item14
                      :imageType="canvasItem.backgroundImage"
                      :showType="item.mapType"
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :toData="item.value"
                      :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                      :width="item.w * colWidth + (item.w - 1) * marginBoder"
                      @changeMap="changeMap(item, $event)"
                    ></card-item14>
                  </card-border>

                  <!-- 右侧显示环形图 -->
                  <card-border
                    v-if="item.chartValue === '20'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item20
                      :echartId="item.itemKey"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :total="item.value.total"
                      :list="item.value.list"
                      :isFull="!isFull && !isFullScreen"
                    ></card-item20>
                  </card-border>

                  <!-- 条形图3 上下布局 -->
                  <card-border
                    v-if="item.chartValue === '15'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item15
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                      :itemKey="item.itemKey"
                      :allCanvs="allCanvs"
                    ></card-item15>
                  </card-border>

                  <card-border
                    v-if="item.chartValue === '21'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item21
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                      :itemKey="item.itemKey"
                    ></card-item21>
                  </card-border>

                  <!-- 双环形图 -->
                  <card-border
                    v-if="item.chartValue === '16'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item16
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :isFull="!isFull && !isFullScreen"
                    ></card-item16>
                  </card-border>

                  <!-- 横向柱状图 -->
                  <card-border
                    v-if="item.chartValue === '17'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item17
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :echartId="item.itemKey"
                    ></card-item17>
                  </card-border>

                  <card-border
                    v-if="item.chartValue === '18'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item18
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                    ></card-item18>
                  </card-border>

                  <card-border
                    v-if="item.chartValue === '19'"
                    :ref="'comp' + item.i"
                    :isBorder="item.isBorder"
                    :showDel="showDel"
                    @deleteCard="deleteCard(item.i)"
                    :height="item.h * rowHeight + (item.h - 1) * marginBoder"
                  >
                    <card-item19
                      :isFull="!isFull && !isFullScreen"
                      :title="lang === 'en' ? item.itemEnName : item.itemName"
                      :list="item.value"
                      :echartId="item.itemKey"
                    ></card-item19>
                  </card-border>
                </grid-item>
              </grid-layout>
            </div>
          </div>

          <!-- 分享showShare -->
          <aside
            v-if="showShare"
            style="height: 100vh"
            class="aside-box share-box"
          >
            <v-card class="w-100 h-100 theme--dark">
              <v-list class="theme--dark">
                <v-subheader class="d-flex align-center justify-space-between">
                  <div>{{ $t('action.share') }}</div>
                  <v-btn icon @click.stop="showShare = false">
                    <vsoc-icon
                      :size="$getCeilSize(32)"
                      type="fill"
                      style="color: #ffffff"
                      icon="icon-guanbi"
                    ></vsoc-icon>
                  </v-btn>
                </v-subheader>
                <div class="mt-6 switch-box d-flex justify-space-between">
                  <v-text-field
                    outlined
                    v-model="shareToken"
                    class="theme--dark"
                    dense
                    label="Token"
                    hide-details
                  >
                  </v-text-field>
                  <v-btn class="item-text ml-3" @click.stop="changeShareToken">
                    {{ $t('grid.link') }}
                  </v-btn>
                </div>
                <div class="mt-6 item-color item-text">
                  {{ $t('grid.headers.url') }}
                </div>
                <div class="mt-2 item-color item-text1">
                  {{ $t('grid.share.tip') }}
                </div>
                <v-text-field
                  class="mt-6"
                  outlined
                  v-model="shareUrl"
                  dense
                  hide-details
                  :disabled="!shareUrl"
                >
                </v-text-field>
                <v-btn
                  class="mt-6 item-text w-100"
                  v-copy="shareUrl"
                  :disabled="!shareUrl"
                >
                  {{ $t('grid.share.copyBtn') }}
                </v-btn>
              </v-list>
            </v-card>
          </aside>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { toDate } from '@/util/filters'
import {
  deepClone,
  exitFullscreen,
  isFullScreen,
  requestFullScreen,
} from '@/util/utils'
import {
  differenceInDays,
  endOfDay,
  format,
  startOfDay,
  subDays,
} from 'date-fns'
import img5 from '../../../assets/images/bg5.png'
import img7 from '../../../assets/images/bg7.png'
import DvDecoration8 from './component/dv-decoration8/Index.vue'
export const ISO_FORMAT = 'yyyy-MM-dd HH:mm:ss'

import { required } from '@/@core/utils/validation'
import {
  canvasDetail,
  getCanvas,
  layoutItemData,
  validLayoutItems,
} from '@/api/grid/index'
import VsocDialog from '@/components/VsocDialog.vue'
import VueGridLayout from 'vue-grid-layout'
import CardBorder from './component/CardBorder.vue'
import CardItem1 from './component/CardItem1.vue'
import CardItem10 from './component/CardItem10.vue'
import CardItem11 from './component/CardItem11.vue'
import CardItem12 from './component/CardItem12.vue'
import CardItem13 from './component/CardItem13.vue'
import CardItem14 from './component/CardItem14.vue'
import CardItem15 from './component/CardItem15.vue'
import CardItem16 from './component/CardItem16.vue'
import CardItem17 from './component/CardItem17.vue'
import CardItem18 from './component/CardItem18.vue'
import CardItem19 from './component/CardItem19.vue'
import CardItem2 from './component/CardItem2.vue'
import CardItem20 from './component/CardItem20.vue'
import CardItem21 from './component/CardItem21.vue'
import CardItem3 from './component/CardItem3.vue'
import CardItem4 from './component/CardItem4.vue'
import CardItem5 from './component/CardItem5.vue'
import CardItem6 from './component/CardItem6.vue'
import CardItem7 from './component/CardItem7.vue'
import CardItem8 from './component/CardItem8.vue'
import CardItem9 from './component/CardItem9.vue'
import { anhuiDefaultPreviewData } from './util/defaultPreview'
const GridLayout = VueGridLayout.GridLayout
const GridItem = VueGridLayout.GridItem
import { getAutomakerDetail } from '@/api/asset/automaker'
//判断是否是全屏状态
let isFull =
  Math.abs(
    window.screen.height - window.document.documentElement.clientHeight,
  ) <= 17

// 阻止F11键默认事件，用HTML5全屏API代替
window.addEventListener('keydown', function (e) {
  e = e || window.event
  if (e.keyCode === 122 && !isFull) {
    e.preventDefault()
    // enterFullScreen()
    requestFullScreen(document.documentElement)
  }
})

export default {
  name: 'detail-screen',
  components: {
    CardItem1,
    CardItem2,
    CardItem3,
    CardItem4,
    CardItem5,
    CardItem6,
    CardItem7,
    CardItem8,
    CardItem9,
    CardItem10,
    CardItem11,
    CardItem12,
    CardItem13,
    CardItem14,
    CardItem15,
    CardItem16,
    CardItem17,
    CardItem18,
    CardItem19,
    CardItem20,
    CardItem21,
    GridLayout,
    GridItem,
    DvDecoration8,
    CardBorder,
    VsocDialog,
  },
  data() {
    return {
      modelImgs: [],
      titleList: [
        {
          text: '资产态势',
          value: '1',
        },
        {
          text: '安全态势',
          value: '2',
        },
      ],
      isShowJump: false,
      imgs: [img5, img7],
      required,
      valid: true,
      showShare: false,
      switchShow: false,
      showUrl: false,
      shareUrl: '',
      shareToken: '',
      selectItems: [],
      saveLoading: false,
      showDel: false,
      editForm: {
        itemName: '',
      },
      lang: '',
      canvasItem: {
        title: '',
        backgroundImage: '',
        titleEnName: '',
      },
      oldBackgroundImage: '',
      // imgs: [img1, img2, img3, img4],
      historyData: [],
      hisAllData: [],
      gridData: [],
      resourcesData: [],
      layoutData: [],
      xList: [],
      // 布局二维数组地图
      layoutMap: [],
      // 布局列数
      layoutColNum: 29,
      minRows: 29,
      rowHeight: 0, //行高
      colWidth: 0,
      isDraggable: false,
      isResizable: false,
      isMirrored: false,
      verticalCompact: true,
      // margin: [24, 24],
      useCssTransforms: true,
      showCard: false,

      refreshDate: null,
      timer2: null,
      isFull: isFull,
      isFullScreen: isFullScreen(),
      currentDate: 7,
      primary: '#44e2fe',
      dateDay: null,
      dateYear: null,
      dateWeek: null,
      timer: null,
      isLoading: false,
      isPageLoading: false,
      // 获取浏览器可视区域高度（包含滚动条）、
      // 获取浏览器可视区域高度（不包含工具栏高度）、
      // 获取body的实际高度  (三个都是相同，兼容性不同的浏览器而设置的)
      screenHeight:
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight,
      screenWidth:
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth,
      currentTypeList: [],
      currentModelType: '',

      showGrid: false,
      selectGrid: '',
      resourceGrid: [{ type: 1 }, { type: 2 }],
      continueDay: '',
      continueObj: {},
      vehicleType: [],
      lastVehicle: '',
      allCanvs: [],
    }
  },
  computed: {
    marginBoder() {
      // return this.$getCeilSize(12)
      return 12
    },
    chartEnum() {
      return this.$store.state.enums.enums['Layout Item Chart Type']
    },
    weekday() {
      return [
        this.$t('screen.week.sun'),
        this.$t('screen.week.mon'),
        this.$t('screen.week.tue'),
        this.$t('screen.week.wed'),
        this.$t('screen.week.thu'),
        this.$t('screen.week.fri'),
        this.$t('screen.week.sat'),
      ]
    },
    currentText() {
      return this.dateOption.find(v => v.value === this.currentDate)?.text
    },
    currentModeItem() {
      let findModeItem = this.vehicleType.find(
        v => v.value === this.currentModelType,
      )
      return findModeItem
      // let text = ''
      // if (this.currentTypeList.length) {
      //   text = `${this.$t('global.selected')}${this.currentTypeList.length}`
      // }
      // return text
    },
    dateOption() {
      return [
        {
          text: this.$t('enums.datePresets.last7'),
          value: 7,
        },
        {
          text: this.$t('enums.datePresets.last15'),
          value: 15,
        },
        {
          text: this.$t('enums.datePresets.last30'),
          value: 30,
        },
      ]
    },

    // vehicleType() {
    //   return this.$store.getters['enums/getDataVehicleType'].filter(
    //     v => v.text !== 'ALL',
    //   )
    // },
  },
  watch: {
    // 'shareForm.endDate'(val) {
    //   this.shareForm.endDate1 = val
    //     ? format(new Date(val), 'yyyy-MM-dd HH:mm:ss')
    //     : ''
    // },
    '$i18n.locale': {
      handler(lang) {
        this.lang = lang
        window.document.documentElement.setAttribute('data-lang', lang)
      },
      deep: true,
      immediate: true,
    },
    '$route.query.id': {
      handler(newVal, oldVal) {
        this.layoutData = []
        this.init()
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // this.init()
  },
  mounted() {
    this.$nextTick(() => {
      if (!isFull || !isFullScreen()) {
        requestFullScreen(document.documentElement)
      }
    })
    this.timeInterval()
    window.addEventListener('resize', this.setRowHeight)
    // let time =
    //   this.$store.getters['enums/getPollingInterval'](
    //     this.$generateMenuTitle(this.$route.meta),
    //   ) || 20
    //60000ms=1
    let time = 60
    this.timer2 = setInterval(() => {
      if (!this.isPageLoading) {
        this.loadList(this.layoutData, '', 'false')
      }
    }, time * 1000)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setRowHeight)
    clearInterval(this.timer)
    if (this.timer2) {
      clearInterval(this.timer2)
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 通过 `vm` 访问组件实例
      if (!isFull || !isFullScreen()) {
        requestFullScreen(document.documentElement)
      }
    })
  },
  beforeRouteLeave(to, from, next) {
    if (isFull || isFullScreen()) {
      exitFullscreen()
    }
    next()
  },
  methods: {
    jumpPage(item) {
      if (item.value === this.canvasItem.backgroundImage) return
      let findItem = this.allCanvs.find(v => v.backGroundImg === item.value)
      if (findItem) {
        this.$router.push(`/grid/shareCanvas?id=${findItem.id}`)
      }
    },
    init() {
      if (!this.$route.query.id) return
      if (this.$route.query.token) {
        this.token = this.$route.query.token
      }
      this.isLoading = true
      //获取所有画布
      this.getAllCanvs()
      //获取所有车企
      this.getAllVehicle()
      //获取车企详情
      // this.getModelDetail()
      this.initCanvas(this.$route.query.id, () => {
        this.intCardList()
      })
    },
    async getAllCanvs() {
      try {
        const res = await getCanvas({ name: '' })
        this.allCanvs = res.data
      } catch (e) {
        console.error(`获取画布管理管理：${e}`)
      }
    },
    changeMap(item, type) {
      let layoutData = []
      if (type === 1) {
        layoutData = deepClone(
          this.hisAllData.filter(
            v => !['C017', 'C018', 'C019', 'C020'].includes(v.itemKey),
          ),
        )
      } else if (type === 2) {
        //境内TOP
        layoutData = deepClone(
          this.hisAllData.filter(
            v => !['C014', 'C015', 'C012', 'C018', 'C020'].includes(v.itemKey),
          ),
        )
      } else if (type === 3) {
        layoutData = deepClone(
          this.hisAllData.filter(
            v => !['C014', 'C015', 'C012', 'C017', 'C019'].includes(v.itemKey),
          ),
        )
      }
      this.loadList(layoutData, '', 'false')
    },

    //获取车型
    async getAllVehicle() {
      const data = await this.$store.dispatch('global/loadAllAutomaker', {
        is_supervise: '0',
      })
      this.vehicleType = data || []
      this.currentModelType =
        this.$route.query.currentModelType || this.vehicleType[0].value
    },

    //获取车型详情
    async getModelDetail() {
      const data = await getAutomakerDetail({ id: this.currentModelType })
      this.modelImgs = data.data.previewAttachments || []
    },

    changeShareToken() {
      if (!this.shareToken) {
        return this.$notify.info('error', this.$t('grid.hint'))
      }
      // http://172.20.1.141:8090/vsocwebdev15/grid/shareCanvas?id=1997103293075030016&token=bearer 12110e85-1d55-4646-87be-d32461e519c2
      this.shareUrl =
        window.location.href?.split('grid')[0] +
        `grid/shareCanvas?id=${this.$route.query.id}&token=${this.shareToken}`
      this.$notify.info('success', this.$t('grid.hint1'))
    },
    shareScreen() {
      this.showShare = true
      this.shareUrl = ''
      this.shareToken = ''
    },

    //获取画布详情
    async initCanvas(id, callBack) {
      try {
        const res = await canvasDetail({ id: id }, this.token)
        this.canvasItem.title = res.data.title
        this.canvasItem.titleEnName = res.data.titleEnName
        this.canvasItem.backgroundImage = res.data.backGroundImg
        this.historyData = res.data.data || []
        this.isLoading = false
        callBack && callBack()
      } catch (err) {
        this.isLoading = false
      }
    },

    //获取所有统计指标有效的数据
    async intCardList() {
      const res = await validLayoutItems(this.editForm)
      let resourcesData = []
      if (res.data.length) {
        resourcesData = res.data.map(v => {
          return {
            ...v,
            x: 0,
            y: 0,
            w: v.startWidth,
            h: v.startHeight,
            i: v.itemKey,
            minW: v.minWidth,
            minH: v.minHeight,
          }
        })
      }
      this.resourcesData = resourcesData

      let defaultList =
        anhuiDefaultPreviewData.find(
          v => v.type === this.canvasItem.backgroundImage,
        )?.defaultList || []

      if (defaultList.length) {
        defaultList = defaultList.map(v => {
          let findItem = resourcesData.find(item => item.itemKey === v.itemKey)
          return {
            ...findItem,
            ...v,
          }
        })
        this.hisAllData = defaultList

        if (this.canvasItem.backgroundImage === '2') {
          defaultList = deepClone(this.hisAllData).filter(
            v => !['C017', 'C018', 'C019', 'C020'].includes(v.itemKey),
          )
        }

        this.loadList(
          defaultList,
          () => {
            this.isLoading = false
          },
          'false',
        )
      }
    },

    //获取动态指标
    async loadList(loadData1, callback, loading) {
      //每次循环的时候，看是否为3的大屏，只有3的大屏有数据，如果为静态数据，则取静态数据，如果不是则取动态
      try {
        this.isPageLoading = true
        this.refreshDate = new Date()

        let list = deepClone(loadData1)
        // let loadData = list.filter(v => v.itemKey !== 'C015')

        if (this.canvasItem.backgroundImage === '3') {
          // C031为已运营
          let findDayItem = this.resourcesData.find(v => v.itemKey === 'C031')
          if (findDayItem) {
            if (findDayItem.contentSourceType === '0') {
              // this.continueObj.value = findDayItem.value
              this.continueDay = differenceInDays(
                new Date(),
                new Date(findDayItem.value),
              )
            } else {
              list.push(findDayItem)
            }
          }
        }

        let contentSourceTypeList = list.filter(
          v => v.contentSourceType === '1',
        )
        if (contentSourceTypeList.length) {
          const params = {
            itemKeyList: contentSourceTypeList.map(v => v.i),
          }
          if (['2', '3'].includes(this.canvasItem.backgroundImage)) {
            params.startDate = format(
              startOfDay(subDays(new Date(), this.currentDate - 1)),
              ISO_FORMAT,
            )
            params.endDate = format(endOfDay(new Date()), ISO_FORMAT)
          }
          if (this.canvasItem.backgroundImage === '3') {
            params.companyCodes = [this.currentModelType]
          }
          const res = await layoutItemData(params, this.token, loading)
          list.forEach(item => {
            let findItem = res.data.find(v => v.itemKey === item.i)
            if (findItem) {
              item.value = findItem.data
            }
          })

          let findDay = res.data.find(v => v.itemKey === 'C031')
          if (findDay) {
            // this.continueObj = findDay
            // this.continueObj.value = findDay.data
            this.continueDay = differenceInDays(
              new Date(),
              new Date(findDay.data),
            )
          }
        }
        this.xList = []
        for (let i = this.currentDate - 1; i >= 0; i--) {
          this.xList.push(format(subDays(new Date(), i), 'MM/dd'))
        }
        let loadData = list.filter(v => v.itemKey !== 'C031')
        this.initCard(loadData)
      } catch (err) {
        console.log('数据大屏数据报错', err)
      } finally {
        this.isPageLoading = false
        callback && callback()
      }
    },

    initCard(loadData) {
      setTimeout(() => {
        this.layoutData = deepClone(loadData)
        this.setRowHeight()
        this.resizedEvent()
      }, 500)
    },
    layoutReadyEvent() {
      this.layoutMap = this.genereatePlaneArr(this.layoutData)
      this.setRowHeight()
    },
    // 当插件内容布局发生变化后  获取现在的二维地图树
    layoutUpdatedEvent() {
      this.layoutMap = this.genereatePlaneArr(this.layoutData)
      setTimeout(() => {
        this.resizedEvent()
      }, 300)
    },
    setRowHeight() {
      this.$nextTick(() => {
        let ele = this.$refs['layout-box']
        let allHeight = ele && ele.offsetHeight
        // let marginBoder = 14
        let height = allHeight - (this.minRows + 1) * this.marginBoder
        this.rowHeight = Math.floor(height / this.minRows)

        let allWidth = ele && ele.offsetWidth
        let width = allWidth - (this.layoutColNum + 1) * this.marginBoder
        this.colWidth = Math.floor(width / this.layoutColNum)
        // console.log(this.colWidth, 111)
      })
    },

    resizedEvent() {
      this.$nextTick(() => {
        this.layoutData.forEach(item => {
          if (
            item.isBorder === '0' &&
            this.$refs['comp' + item.i] &&
            this.$refs['comp' + item.i].length
          ) {
            this.$refs['comp' + item.i][0].$children.length &&
              this.$refs['comp' + item.i][0].$children[0].initWH()
          }
        })
      })
    },
    // 生成二维数组地图
    genereatePlaneArr(data) {
      var map = []
      if (Array.isArray(data)) {
        for (var i = 0; i < data.length; i++) {
          var one = data[i]
          // 循环行
          for (var r = one.y; r < one.y + one.h; r++) {
            // 循环列
            for (var c = one.x; c < one.x + one.w; c++) {
              // 检修当前行是否存在
              if (!map[r]) {
                map[r] = new Array(this.layoutColNum)

                for (let i = 0; i < this.layoutColNum; i++) {
                  map[r][i] = 0
                }
              }
              // 占据为1
              map[r][c] = 1
            }
          }
        }
      }
      return map
    },
    changeVehicles(value) {
      if (!value) return
      this.currentModelType = value
      // this.getModelDetail()
      this.loadList(this.layoutData, () => {})
      // if (this.currentTypeList.length === 1) {
      //   this.lastVehicle = this.currentTypeList[0]
      // } else if (this.currentTypeList.length === 0) {
      //   this.currentTypeList = [this.lastVehicle]
      // }
    },
    // onSelectData(value) {
    //   if (!value) return
    //   // this.currentTypeList = value
    //   // this.loadList(this.layoutData)
    //   // this.isLoading = true
    //   this.loadList(this.layoutData, () => {
    //     // this.isLoading = false
    //   })
    // },
    onSelectData(value) {
      if (!value) return
      this.currentDate = value
      // this.loadList(this.layoutData)
      // this.isLoading = true
      this.loadList(this.layoutData, () => {
        // this.isLoading = false
      })
    },
    toggleFullScreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
    },
    timeInterval() {
      this.timer = setInterval(() => {
        this.dateDay = toDate(new Date(), 'hh:mm:ss')
        this.dateYear = toDate(new Date(), 'yyyy/MM/dd')
        this.dateWeek = this.weekday[new Date().getDay()]
      }, 1000)
    },
    goIndex() {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
      this.$router.push('/')
    },
    getScreenSize() {
      this.getScreenHeight()
      this.getScreenWidth()
      isFull =
        Math.abs(
          window.screen.height - window.document.documentElement.clientHeight,
        ) <= 17
      this.isFull = isFull
      this.isFullScreen = isFullScreen()
    },
    // 获取浏览器高度进行自适应
    getScreenHeight() {
      this.screenHeight =
        window.innerHeight ||
        document.documentElement.innerHeight ||
        document.body.clientHeight
    },
    // 字体大小根据宽度自适应
    getScreenWidth() {
      this.screenWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth
      this.$store.commit('global/setClientWidth', this.screenWidth)
    },
  },
}
</script>

<style lang="scss">
@import './index.scss';
.vue-grid-item {
  transition: none !important; /* 禁用 vue-grid-item 的过渡效果 */
}
.time-box {
  position: relative;

  .el-date-editor {
    position: absolute;
    top: 8px;
    left: 0;
    width: 90%;
  }
}
</style>
